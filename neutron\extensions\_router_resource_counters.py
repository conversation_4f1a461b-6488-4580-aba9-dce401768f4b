#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api import converters
from neutron_lib.api.definitions import l3

# Resource counters constants
RESOURCE_COUNTER = 'RESOURCE_COUNTER'
RESOURCE_NAME = 'resource_counter'
COLLECTION_NAME = 'resource_counters'

# Alias for the extension
ALIAS = 'router-resource-counters'
IS_SHIM_EXTENSION = False
IS_STANDARD_ATTR_EXTENSION = False
NAME = 'Router Resource Counters Extension'
API_PREFIX = ''
DESCRIPTION = 'Adds resource counters to router resources'
UPDATED_TIMESTAMP = '2025-03-05T00:00:00-00:00'

# Required API extensions
REQUIRED_EXTENSIONS = [l3.ALIAS]
OPTIONAL_EXTENSIONS = []

# Extended attributes for the router resource
RESOURCE_ATTRIBUTE_MAP = {
    l3.ROUTERS: {
        'resource_counters': {
            'allow_post': False,
            'allow_put': False,
            'is_visible': True,
            'default': None,
            'validate': {'type:dict': None}
        }
    }
}

# Sub-resource API definition
SUB_RESOURCE_ATTRIBUTE_MAP = {
    COLLECTION_NAME: {
        'parent': {'collection_name': l3.ROUTERS,
                   'member_name': l3.ROUTER},
        'parameters': {
            'port_forwarding': {
                'allow_post': False,
                'allow_put': False,
                'is_visible': True,
                'default': 0,
                'convert_to': converters.convert_to_int
            },
            'floating_ip': {
                'allow_post': False,
                'allow_put': False,
                'is_visible': True,
                'default': 0,
                'convert_to': converters.convert_to_int
            },
            'elastic_snat': {
                'allow_post': False,
                'allow_put': False,
                'is_visible': True,
                'default': 0,
                'convert_to': converters.convert_to_int
            },
            'router_interface': {
                'allow_post': False,
                'allow_put': False,
                'is_visible': True,
                'default': 0,
                'convert_to': converters.convert_to_int
            }
        }
    }
}
