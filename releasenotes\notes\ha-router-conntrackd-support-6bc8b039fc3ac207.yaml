---
features:
  - |
    HA routers can now run "conntrackd" in addition to "keepalived" to
    synchronize connection tracking states across router instances. This
    ensures that established connections survive a HA router failover. L3 agent
    hosts must have the "conntrackd" binary installed.

    Conntrackd support is not enabled by default and can be enabled by setting
    the ha_conntrackd_enabled option to true.
