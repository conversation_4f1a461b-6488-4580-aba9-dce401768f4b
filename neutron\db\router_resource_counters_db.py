# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

from neutron_lib.api.definitions import l3 as l3_apidef
from neutron_lib import context
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_log import log as logging

from neutron.conf.extensions import router_resource_counters as rrc_conf
from neutron.db import _resource_extend as resource_extend
from neutron.extensions import _elastic_snat as esnat_apidef

cfg.CONF.register_opts(rrc_conf.router_resource_counters_opts)

LOG = logging.getLogger(__name__)


class RouterResourceCountersMixin(object):
    """Mixin class for router resource counters."""

    def get_resource_counters(self, context, router_id):
        """Calculate resource counts for a router."""
        return {
            'port_forwarding': self._get_pf_count(context, router_id),
            'floating_ip': self._get_fip_count(context, router_id),
            'router_interface': self._get_interface_count(context, router_id),
            'elastic_snat': self._get_elastic_snat_count(context, router_id)
        }

    def _get_pf_count(self, context, router_id):
        """Get port forwarding count for a router."""
        pf_plugin = directory.get_plugin(constants.PORTFORWARDING)
        if not pf_plugin:
            return 0
        return pf_plugin._get_pf_count_for_router(context, router_id)

    def _get_fip_count(self, context, router_id):
        """Get floating IP count for a router."""
        # For L3RouterPlugin, self is the L3 plugin
        return self.get_floatingips_count(
            context, {'router_id': [router_id]})

    def _get_interface_count(self, context, router_id):
        """Get router interface count for a router."""
        core_plugin = directory.get_plugin()
        if not core_plugin:
            return 0
        return core_plugin.get_ports_count(context, filters={
            'device_id': [router_id]
        })

    def _get_elastic_snat_count(self, context, router_id):
        """Get elastic SNAT count for a router."""
        esnat_plugin = directory.get_plugin(esnat_apidef.ELASTIC_SNAT)
        if not esnat_plugin:
            return 0
        return esnat_plugin._get_elastic_snat_count_for_router(
            context, router_id
        )

    @staticmethod
    @resource_extend.extends([l3_apidef.ROUTERS])
    def _extend_router_dict_resource_counters(router_res, router_db):
        """Extend router dictionary with resource counters."""
        # Skip if extended_router resource counters are disabled
        if not cfg.CONF.enable_extend_router_resource_counters_in_router:
            return router_res

        l3_plugin = directory.get_plugin(constants.L3)
        if l3_plugin:
            admin_context = context.get_admin_context()
            router_res['resource_counters'] = l3_plugin.get_resource_counters(
                admin_context, router_res['id'])
        return router_res
