# Copyright (c) 2014 OpenStack Foundation.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import sys

from oslo_config import cfg
from oslo_db import options as db_options
from oslo_log import log as logging

from neutron._i18n import _
from neutron.common import config
from neutron import server
from sqlalchemy import create_engine
from sqlalchemy import MetaData
from sqlalchemy import Table


LOG = logging.getLogger(__name__)


def setup_conf():
    config.setup_logging()


def get_db_connection():
    db_url = cfg.CONF.database.connection
    engine = create_engine(db_url)
    return engine


def query_subnets(engine, cidr):
    metadata = MetaData()
    subnets_table = Table('subnets', metadata, autoload_with=engine)

    with engine.connect() as conn:
        query = subnets_table.select().where(subnets_table.c.cidr.like(cidr))
        result = conn.execute(query)
        rows = result.fetchall()

        if not rows:
            print("No subnets found matching CIDR: %s" % cidr)
            return

        columns = rows[0].keys()

        col_widths = {}
        for col in columns:
            max_data_len = max(len(unicode(row[col])) for row in rows)
            col_widths[col] = max(len(col), max_data_len)

        header = " | ".join(col.ljust(col_widths[col]) for col in columns)
        separator = "-+-".join('-' * col_widths[col] for col in columns)
        print(header)
        print(separator)

        for row in rows:
            line = " | ".join(unicode(row[col]).ljust(col_widths[col])
                              for col in columns)
            print(line.encode('utf-8'))


CIDR_OPT = [
    cfg.StrOpt('cidr', default='%',
               help=_('CIDR pattern to filter subnets, e.g.,'
                      '192.168.100.0/24, 100.123.%%'))
]


def main():
    try:
        setup_conf()
        conf_files = server._get_config_files()
        cfg.CONF.register_cli_opts(CIDR_OPT)
        config.init(sys.argv[1:], default_config_files=conf_files)
        db_options.set_defaults(cfg.CONF)

        engine = get_db_connection()

        cidr = cfg.CONF.cidr
        LOG.info("Querying subnets with CIDR like '{}'".format(cidr))
        query_subnets(engine, cidr)

    except Exception as e:
        LOG.error("Error occurred: {}".format(str(e)))
        sys.exit(1)


if __name__ == "__main__":
    main()
