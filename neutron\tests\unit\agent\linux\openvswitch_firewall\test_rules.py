# Copyright 2015 Red Hat, Inc.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import constants
from oslo_config import cfg

from neutron.agent import firewall
from neutron.agent.linux.openvswitch_firewall import constants as ovsfw_consts
from neutron.agent.linux.openvswitch_firewall import firewall as ovsfw
from neutron.agent.linux.openvswitch_firewall import rules
from neutron.common import constants as n_const
from neutron.plugins.ml2.drivers.openvswitch.agent.common import constants \
        as ovs_consts
from neutron.tests import base

TESTING_VLAN_TAG = 1
INGRESS_ACCEPT = (constants.INGRESS_DIRECTION, ovsfw_consts.ACTION_ACCEPT)
INGRESS_DENY = (constants.INGRESS_DIRECTION, ovsfw_consts.ACTION_DENY)
EGRESS_ACCEPT = (constants.EGRESS_DIRECTION, ovsfw_consts.ACTION_ACCEPT)
EGRESS_DENY = (constants.EGRESS_DIRECTION, ovsfw_consts.ACTION_DENY)
FLOW_ENABLE_INGRESS = (True, constants.INGRESS_DIRECTION)
FLOW_DISABLE_INGRESS = (False, constants.INGRESS_DIRECTION)
FLOW_ENABLE_EGRESS = (True, constants.EGRESS_DIRECTION)
FLOW_DISABLE_EGRESS = (False, constants.EGRESS_DIRECTION)


class TestIsValidPrefix(base.BaseTestCase):
    def test_valid_prefix_ipv4(self):
        is_valid = rules.is_valid_prefix('10.0.0.0/0')
        self.assertTrue(is_valid)

    def test_invalid_prefix_ipv4(self):
        is_valid = rules.is_valid_prefix('0.0.0.0/0')
        self.assertFalse(is_valid)

    def test_valid_prefix_ipv6(self):
        is_valid = rules.is_valid_prefix('ffff::0/0')
        self.assertTrue(is_valid)

    def test_invalid_prefix_ipv6(self):
        is_valid = rules.is_valid_prefix('0000:0::0/0')
        self.assertFalse(is_valid)
        is_valid = rules.is_valid_prefix('::0/0')
        self.assertFalse(is_valid)
        is_valid = rules.is_valid_prefix('::/0')
        self.assertFalse(is_valid)


class TestCreateFlowsFromRuleAndPort(base.BaseTestCase):
    def setUp(self):
        super(TestCreateFlowsFromRuleAndPort, self).setUp()
        ovs_port = mock.Mock(vif_mac='00:00:00:00:00:00')
        ovs_port.ofport = 1
        port_dict = {'device': 'port_id'}
        self.port = ovsfw.OFPort(
            port_dict, ovs_port, vlan_tag=TESTING_VLAN_TAG)

        self.create_flows_mock = mock.patch.object(
            rules, 'create_protocol_flows').start()

    @property
    def passed_flow_template(self):
        return self.create_flows_mock.call_args[0][1]

    def _test_create_flows_from_rule_and_port_helper(
            self, rule, expected_template):
        rules.create_flows_from_rule_and_port(rule, self.port)

        self.assertEqual(expected_template, self.passed_flow_template)

    def test_create_flows_from_rule_and_port_no_ip_ipv4(self):
        rule = {
            'ethertype': constants.IPv4,
            'direction': constants.INGRESS_DIRECTION,
        }
        expected_template = {
            'priority': 74,
            'dl_type': n_const.ETHERTYPE_IP,
            'reg_port': self.port.ofport,
        }
        self._test_create_flows_from_rule_and_port_helper(rule,
                                                          expected_template)

    def test_create_flows_from_rule_and_port_src_and_dst_ipv4(self):
        rule = {
            'ethertype': constants.IPv4,
            'direction': constants.INGRESS_DIRECTION,
            'source_ip_prefix': '***********/24',
            'dest_ip_prefix': '********/32',
        }
        expected_template = {
            'priority': 74,
            'dl_type': n_const.ETHERTYPE_IP,
            'reg_port': self.port.ofport,
            'nw_src': '***********/24',
            'nw_dst': '********/32',
        }
        self._test_create_flows_from_rule_and_port_helper(rule,
                                                          expected_template)

    def test_create_flows_from_rule_and_port_src_and_dst_with_zero_ipv4(self):
        rule = {
            'ethertype': constants.IPv4,
            'direction': constants.INGRESS_DIRECTION,
            'source_ip_prefix': '***********/24',
            'dest_ip_prefix': '0.0.0.0/0',
        }
        expected_template = {
            'priority': 74,
            'dl_type': n_const.ETHERTYPE_IP,
            'reg_port': self.port.ofport,
            'nw_src': '***********/24',
        }
        self._test_create_flows_from_rule_and_port_helper(rule,
                                                          expected_template)

    def test_create_flows_from_rule_and_port_no_ip_ipv6(self):
        rule = {
            'ethertype': constants.IPv6,
            'direction': constants.INGRESS_DIRECTION,
        }
        expected_template = {
            'priority': 74,
            'dl_type': n_const.ETHERTYPE_IPV6,
            'reg_port': self.port.ofport,
        }
        self._test_create_flows_from_rule_and_port_helper(rule,
                                                          expected_template)

    def test_create_flows_from_rule_and_port_src_and_dst_ipv6(self):
        rule = {
            'ethertype': constants.IPv6,
            'direction': constants.INGRESS_DIRECTION,
            'source_ip_prefix': '2001:db8:bbbb::1/64',
            'dest_ip_prefix': '2001:db8:aaaa::1/64',
        }
        expected_template = {
            'priority': 74,
            'dl_type': n_const.ETHERTYPE_IPV6,
            'reg_port': self.port.ofport,
            'ipv6_src': '2001:db8:bbbb::1/64',
            'ipv6_dst': '2001:db8:aaaa::1/64',
        }
        self._test_create_flows_from_rule_and_port_helper(rule,
                                                          expected_template)

    def test_create_flows_from_rule_and_port_src_and_dst_with_zero_ipv6(self):
        rule = {
            'ethertype': constants.IPv6,
            'direction': constants.INGRESS_DIRECTION,
            'source_ip_prefix': '2001:db8:bbbb::1/64',
            'dest_ip_prefix': '::/0',
        }
        expected_template = {
            'priority': 74,
            'dl_type': n_const.ETHERTYPE_IPV6,
            'reg_port': self.port.ofport,
            'ipv6_src': '2001:db8:bbbb::1/64',
        }
        self._test_create_flows_from_rule_and_port_helper(rule,
                                                          expected_template)


class TestCreateProtocolFlows(base.BaseTestCase):
    def setUp(self):
        super(TestCreateProtocolFlows, self).setUp()
        ovs_port = mock.Mock(vif_mac='00:00:00:00:00:00')
        ovs_port.ofport = 1
        port_dict = {'device': 'port_id'}
        self.port = ovsfw.OFPort(
            port_dict, ovs_port, vlan_tag=TESTING_VLAN_TAG)

    def _test_create_protocol_flows_helper(self, direction, rule,
                                           expected_flows):
        flow_template = {'some_settings': 'foo'}
        for flow in expected_flows:
            flow.update(flow_template)
        flows = rules.create_protocol_flows(
            direction, flow_template, self.port, rule)
        self.assertEqual(expected_flows, flows)

    def test_create_protocol_flows_ingress(self):
        rule = {'protocol': constants.PROTO_NUM_TCP}
        expected_flows = [{
            'table': ovs_consts.RULES_INGRESS_TABLE,
            'actions': 'output:1',
            'nw_proto': constants.PROTO_NUM_TCP,
        }]
        self._test_create_protocol_flows_helper(
            constants.INGRESS_DIRECTION, rule, expected_flows)

    def test_create_protocol_flows_egress(self):
        rule = {'protocol': constants.PROTO_NUM_TCP}
        expected_flows = [{
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,{:d})'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE),
            'nw_proto': constants.PROTO_NUM_TCP,
        }]
        self._test_create_protocol_flows_helper(
            constants.EGRESS_DIRECTION, rule, expected_flows)

    def test_populate_flow_common_ingress_accept(self):
        expected_flows = {
            'table': ovs_consts.RULES_INGRESS_TABLE,
            'actions': 'output:1'
        }
        flow_template = {}
        flows = rules.populate_flow_common(
            constants.INGRESS_DIRECTION, flow_template, self.port,
            ovsfw_consts.ACTION_ACCEPT)
        self.assertEqual(expected_flows, flows)

    def test_populate_flow_common_ingress_deny(self):
        expected_flows = {
            'table': ovs_consts.RULES_INGRESS_TABLE,
            'actions': 'resubmit(,93)',
        }
        flow_template = {}
        flows = rules.populate_flow_common(
            constants.INGRESS_DIRECTION, flow_template, self.port,
            ovsfw_consts.ACTION_DENY)
        self.assertEqual(expected_flows, flows)

    def test_populate_flow_common_egress_accept(self):
        expected_flows = {
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,73)',
        }
        flow_template = {}
        flows = rules.populate_flow_common(
            constants.EGRESS_DIRECTION, flow_template, self.port,
            ovsfw_consts.ACTION_ACCEPT)
        self.assertEqual(expected_flows, flows)

    def test_populate_flow_common_egress_deny(self):
        expected_flows = {
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,93)',
        }
        flow_template = {}
        flows = rules.populate_flow_common(
            constants.EGRESS_DIRECTION, flow_template, self.port,
            ovsfw_consts.ACTION_DENY)
        self.assertEqual(expected_flows, flows)

    def test_create_icmp_flows_empty(self):
        rule = {'protocol': constants.PROTO_NUM_ICMP,
                'action': ovsfw_consts.ACTION_DENY}
        flow_template = {}
        f = rules.create_icmp_flows(flow_template, rule)
        self.assertEqual([], f)

    def test_create_icmp_flows(self):
        rule = {'protocol': constants.PROTO_NUM_ICMP,
                'port_range_min': 1,
                'port_range_max': 2,
                'action': ovsfw_consts.ACTION_DENY}
        flow_template = {}
        f = rules.create_icmp_flows(flow_template, rule)
        self.assertEqual([{'icmp_type': 1,
                           'icmp_code': 2}], f)

    def test_create_protocol_flows_no_protocol(self):
        rule = {}
        expected_flows = [{
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,{:d})'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE),
        }]
        self._test_create_protocol_flows_helper(
            constants.EGRESS_DIRECTION, rule, expected_flows)

    def test_create_protocol_flows_icmp6(self):
        rule = {'ethertype': constants.IPv6,
                'protocol': constants.PROTO_NUM_IPV6_ICMP}
        expected_flows = [{
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,{:d})'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE),
            'nw_proto': constants.PROTO_NUM_IPV6_ICMP,
        }]
        self._test_create_protocol_flows_helper(
            constants.EGRESS_DIRECTION, rule, expected_flows)

    def test_create_protocol_flows_port_range(self):
        rule = {'ethertype': constants.IPv4,
                'protocol': constants.PROTO_NUM_TCP,
                'port_range_min': 22,
                'port_range_max': 23}
        expected_flows = [{
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,{:d})'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE),
            'nw_proto': constants.PROTO_NUM_TCP,
            'tcp_dst': '0x0016/0xfffe'
        }]
        self._test_create_protocol_flows_helper(
            constants.EGRESS_DIRECTION, rule, expected_flows)

    def test_create_protocol_flows_icmp(self):
        rule = {'ethertype': constants.IPv4,
                'protocol': constants.PROTO_NUM_ICMP,
                'port_range_min': 0}
        expected_flows = [{
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,{:d})'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE),
            'nw_proto': constants.PROTO_NUM_ICMP,
            'icmp_type': 0
        }]
        self._test_create_protocol_flows_helper(
            constants.EGRESS_DIRECTION, rule, expected_flows)

    def test_create_protocol_flows_ipv6_icmp(self):
        rule = {'ethertype': constants.IPv6,
                'protocol': constants.PROTO_NUM_IPV6_ICMP,
                'port_range_min': 5,
                'port_range_max': 0}
        expected_flows = [{
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'actions': 'resubmit(,{:d})'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE),
            'nw_proto': constants.PROTO_NUM_IPV6_ICMP,
            'icmp_type': 5,
            'icmp_code': 0,
        }]
        self._test_create_protocol_flows_helper(
            constants.EGRESS_DIRECTION, rule, expected_flows)


class TestCreatePortRangeFlows(base.BaseTestCase):
    def _test_create_port_range_flows_helper(self, expected_flows, rule):
        flow_template = {'some_settings': 'foo'}
        for flow in expected_flows:
            flow.update(flow_template)
        port_range_flows = rules.create_port_range_flows(flow_template, rule)
        self.assertEqual(expected_flows, port_range_flows)

    def test_create_port_range_flows_with_source_and_destination(self):
        rule = {
            'protocol': constants.PROTO_NUM_TCP,
            'source_port_range_min': 123,
            'source_port_range_max': 124,
            'port_range_min': 10,
            'port_range_max': 11,
        }
        expected_flows = [
            {'tcp_src': '0x007b', 'tcp_dst': '0x000a/0xfffe'},
            {'tcp_src': '0x007c', 'tcp_dst': '0x000a/0xfffe'},
        ]
        self._test_create_port_range_flows_helper(expected_flows, rule)

    def test_create_port_range_flows_with_source(self):
        rule = {
            'protocol': constants.PROTO_NUM_TCP,
            'source_port_range_min': 123,
            'source_port_range_max': 124,
        }
        expected_flows = [
            {'tcp_src': '0x007b'},
            {'tcp_src': '0x007c'},
        ]
        self._test_create_port_range_flows_helper(expected_flows, rule)

    def test_create_port_range_flows_with_destination(self):
        rule = {
            'protocol': constants.PROTO_NUM_TCP,
            'port_range_min': 10,
            'port_range_max': 11,
        }
        expected_flows = [
            {'tcp_dst': '0x000a/0xfffe'},
        ]
        self._test_create_port_range_flows_helper(expected_flows, rule)

    def test_create_port_range_flows_without_port_range(self):
        rule = {
            'protocol': constants.PROTO_NUM_TCP,
        }
        expected_flows = []
        self._test_create_port_range_flows_helper(expected_flows, rule)

    def test_create_port_range_with_icmp_protocol(self):
        # NOTE: such call is prevented by create_protocols_flows
        rule = {
            'protocol': constants.PROTO_NUM_ICMP,
            'port_range_min': 10,
            'port_range_max': 11,
        }
        expected_flows = []
        self._test_create_port_range_flows_helper(expected_flows, rule)


class TestCreateFlowsForIpAddress(base.BaseTestCase):
    def _generate_conjuncion_actions(self, conj_ids, offset):
        return ','.join(
            ["conjunction(%d,1/2)" % (c + offset)
             for c in conj_ids])

    def test_create_flows_for_ip_address_egress(self):
        expected_template = {
            'table': ovs_consts.RULES_EGRESS_TABLE,
            'priority': 72,
            'dl_type': n_const.ETHERTYPE_IP,
            'reg_net': 0x123,
            'nw_dst': '***********/32'
        }

        conj_ids = [12, 20]
        flows = rules.create_flows_for_ip_address(
            ('***********', 'fa:16:3e:aa:bb:cc'),
            constants.EGRESS_DIRECTION, constants.IPv4,
            0x123, conj_ids)

        self.assertEqual(2, len(flows))
        self.assertEqual(ovsfw_consts.OF_STATE_ESTABLISHED_NOT_REPLY,
                         flows[0]['ct_state'])
        self.assertEqual(ovsfw_consts.OF_STATE_NEW_NOT_ESTABLISHED,
                         flows[1]['ct_state'])
        for i in range(2):
            self.assertEqual(self._generate_conjuncion_actions(conj_ids, i),
                             flows[i]['actions'])
        for f in flows:
            del f['actions']
            del f['ct_state']
            self.assertEqual(expected_template, f)


class TestCreateConjFlows(base.BaseTestCase):
    def _test_create_conj_flows(self, action=ovsfw_consts.ACTION_ACCEPT,
                                enable_flow_log=False,
                                direction=constants.INGRESS_DIRECTION):
        cfg.CONF.set_override('enable_flow_log',
                              enable_flow_log,
                              group='FLOW_LOG')
        ovs_port = mock.Mock(ofport=1, vif_mac='00:00:00:00:00:00')
        port_dict = {'device': 'port_id'}
        port = ovsfw.OFPort(
            port_dict, ovs_port, vlan_tag=TESTING_VLAN_TAG,
            flow_log_enabled=enable_flow_log)

        conj_id = 1234
        expected_template = {
            'table': ovs_consts.RULES_INGRESS_TABLE,
            'dl_type': n_const.ETHERTYPE_IPV6,
            'priority': 71,
            'conj_id': conj_id,
            'reg_port': port.ofport
        }
        if direction == constants.EGRESS_DIRECTION:
            expected_template['table'] = ovs_consts.RULES_EGRESS_TABLE

        flows = rules.create_conj_flows(port, conj_id,
                                        direction,
                                        constants.IPv6,
                                        action=action,
                                        enable_flow_log=enable_flow_log)

        self.assertEqual(ovsfw_consts.OF_STATE_ESTABLISHED_NOT_REPLY,
                         flows[0]['ct_state'])
        self.assertEqual(ovsfw_consts.OF_STATE_NEW_NOT_ESTABLISHED,
                         flows[1]['ct_state'])

        set_action = ("{:s}move:NXM_NX_REG6[0..11]->NXM_NX_REG10[0..11],"
                      "resubmit(,{:d})")
        drop_action = 'resubmit(,{:d})'.format(
            ovs_consts.DROPPED_TRAFFIC_TABLE)
        action_suffix = ",{}".format(
            ovsfw_consts.ACTION_SET_ACCEPT) if enable_flow_log else ""
        accept_action = "output:{}{}".format(port.ofport, action_suffix)
        egress_action = "resubmit(,{}){},".format(
            ovs_consts.ACCEPT_OR_INGRESS_TABLE, action_suffix)
        # Test flows according by direction, action type
        direction_action = (direction, action)
        learn_action = ""
        if enable_flow_log:
            if direction_action == INGRESS_ACCEPT:
                learn_action = set_action.format(
                        "", ovs_consts.FLOW_LOG_REENTRY_TABLE)
                expected_action = ','.join([accept_action, learn_action])
            elif direction_action == INGRESS_DENY:
                learn_action = set_action.format(
                    ovsfw_consts.ACTION_SET_DENY + ",",
                    ovs_consts.FLOW_LOG_REENTRY_TABLE)
                expected_action = ','.join([drop_action, learn_action])
            elif direction_action == EGRESS_ACCEPT:
                expected_action = set_action.format(
                        egress_action, ovs_consts.FLOW_LOG_CLASSIFY_TABLE)
            else:
                expected_action = set_action.format(
                    "", ovs_consts.FLOW_LOG_CLASSIFY_TABLE)
        else:
            if direction_action == INGRESS_ACCEPT:
                expected_action = accept_action
            elif direction_action == INGRESS_DENY:
                expected_action = drop_action
            elif direction_action == EGRESS_ACCEPT:
                expected_action = "resubmit(,{:d})".format(
                    ovs_consts.ACCEPT_OR_INGRESS_TABLE)
            else:
                expected_action = "resubmit(,{:d})".format(
                    ovs_consts.DROPPED_TRAFFIC_TABLE)

        self.assertEqual(expected_action, flows[0]['actions'])

        flow_enable_direction = (enable_flow_log, direction)
        ingress_action = 'resubmit(,{:d})'.format(
            ovs_consts.ACCEPTED_INGRESS_TRAFFIC_TABLE)
        if action == ovsfw_consts.ACTION_ACCEPT:
            if flow_enable_direction == FLOW_ENABLE_INGRESS:
                self.assertEqual(
                    "ct(commit,zone=NXM_NX_REG{:d}[0..15]),"
                    "{:s},{:s},{:s}".format(
                        ovsfw_consts.REG_NET,
                        accept_action,
                        learn_action,
                        ingress_action),
                    flows[1]['actions'])
            elif flow_enable_direction == FLOW_ENABLE_EGRESS:
                self.assertEqual(expected_action, flows[1]['actions'])
            elif flow_enable_direction == FLOW_DISABLE_INGRESS:
                self.assertEqual(
                    "ct(commit,zone=NXM_NX_REG{:d}[0..15]),"
                    "{:s},{:s}".format(
                        ovsfw_consts.REG_NET,
                        flows[0]['actions'], ingress_action),
                    flows[1]['actions'])
            else:
                self.assertEqual(expected_action, flows[1]['actions'])
        else:
            if direction == constants.INGRESS_DIRECTION:
                flow_table = ovs_consts.FLOW_LOG_REENTRY_TABLE
            else:
                flow_table = ovs_consts.FLOW_LOG_CLASSIFY_TABLE
            if enable_flow_log:
                deny_action = (
                    'resubmit(,{:d}),{:s},{:s},resubmit(,{:d})'.format(
                        ovs_consts.DROPPED_TRAFFIC_TABLE,
                        ovsfw_consts.ACTION_SET_DENY,
                        ovsfw_consts.ACTION_SET_NET,
                        flow_table))
            else:
                deny_action = 'resubmit(,{:d})'.format(
                        ovs_consts.DROPPED_TRAFFIC_TABLE)
            self.assertEqual(deny_action, flows[1]['actions'])

        for f in flows[:2]:
            del f['actions']
            del f['ct_state']
            if 'proto' not in f:
                self.assertEqual(expected_template, f)
                expected_template['conj_id'] += 1
            else:
                self.assertIn(
                    f['proto'],
                    [firewall.write_proto(constants.IPv6,
                                          constants.PROTO_NAME_TCP),
                     firewall.write_proto(constants.IPv6,
                                          constants.PROTO_NAME_UDP),
                     firewall.write_proto(constants.IPv6,
                                          constants.PROTO_NAME_ICMP)])

        learn_action = (
            'learn(cookie=0,table={table},priority=1,idle_timeout=7200,'
            'eth_type=0x86dd,NXM_OF_ETH_SRC[]=NXM_OF_ETH_SRC[],'
            'NXM_OF_ETH_DST[]=NXM_OF_ETH_DST[],'
            'NXM_NX_IPV6_SRC[]=NXM_NX_IPV6_SRC[],'
            'NXM_NX_IPV6_DST[]=NXM_NX_IPV6_DST[],'
            'NXM_NX_REG10[0..11]),resubmit(,{table})'
        )
        if enable_flow_log:
            if direction_action == INGRESS_DENY:
                flow_action = learn_action.format(
                        table=ovs_consts.FLOW_LOG_ACCEPT_INGRESS_DROP)
            elif direction_action == INGRESS_ACCEPT:
                flow_action = learn_action.format(
                        table=ovs_consts.FLOW_LOG_ACCEPT_INGRESS_OUTPUT)
            elif direction_action == EGRESS_DENY:
                flow_action = learn_action.format(
                    table=ovs_consts.FLOW_LOG_ACCEPT_EGRESS_DROP)
            else:
                flow_action = learn_action.format(
                    table=ovs_consts.FLOW_LOG_ACCEPT_EGRESS_OUTPUT)
            if direction == constants.INGRESS_DIRECTION:
                flow_table = ovs_consts.FLOW_LOG_REENTRY_TABLE
            else:
                flow_table = ovs_consts.FLOW_LOG_CLASSIFY_TABLE
            # Included other 2 flows logs
            self.assertEqual(len(flows), 4)
            for flow in flows[2:]:
                self.assertEqual(flow['actions'], flow_action)
                self.assertEqual(flow['table'], flow_table)
            self.assertEqual(flows[0]['conj_id'], flows[2]['reg7'])
            self.assertEqual(flows[0]['priority'], flows[2]['priority'])
            self.assertEqual(flows[1]['conj_id'], flows[3]['reg7'])
            self.assertEqual(flows[1]['priority'], flows[3]['priority'])
            if direction == constants.INGRESS_DIRECTION:
                self.assertEqual(flows[0]['reg_port'], flows[2]['reg_port'])
                self.assertEqual(flows[1]['reg_port'], flows[3]['reg_port'])
            else:
                self.assertEqual(flows[0]['reg_port'], flows[2]['in_port'])
                self.assertEqual(flows[1]['reg_port'], flows[3]['in_port'])

    def test_create_conj_flows(self):
        self._test_create_conj_flows(ovsfw_consts.ACTION_ACCEPT)

    def test_create_conj_flows_deny(self):
        self._test_create_conj_flows(ovsfw_consts.ACTION_DENY)

    def test_create_conj_flows_enable_flow_log(self):
        self._test_create_conj_flows(ovsfw_consts.ACTION_ACCEPT,
                                     enable_flow_log=True)

    def test_create_conj_flows_deny_enable_flow_log(self):
        self._test_create_conj_flows(ovsfw_consts.ACTION_DENY,
                                     enable_flow_log=True)

    def test_create_conj_flows_egress_accept(self):
        self._test_create_conj_flows(action=ovsfw_consts.ACTION_ACCEPT,
                                     direction=constants.EGRESS_DIRECTION)

    def test_create_conj_flows_egress_deny(self):
        self._test_create_conj_flows(action=ovsfw_consts.ACTION_DENY,
                                     direction=constants.EGRESS_DIRECTION)

    def test_create_conj_flows_egress_accept_enable_flow_log(self):
        self._test_create_conj_flows(action=ovsfw_consts.ACTION_ACCEPT,
                                     enable_flow_log=True,
                                     direction=constants.EGRESS_DIRECTION)


class TestMergeRules(base.BaseTestCase):
    def setUp(self):
        super(TestMergeRules, self).setUp()
        self.rule_tmpl = [('direction', 'ingress'), ('ethertype', 'IPv4'),
                          ('protocol', 6)]

    def _test_merge_port_ranges_helper(self, expected, result):
        """Take a list of (port_range_min, port_range_max, conj_ids)
        and an output from rules.merge_port_ranges and check if they
        are identical, ignoring the other rule fields.
        """
        self.assertEqual(len(expected), len(result))
        for (range_min, range_max, conj_ids), result1 in zip(
                expected, result):
            self.assertEqual(range_min, result1[0].get('port_range_min'))
            self.assertEqual(range_max, result1[0].get('port_range_max'))
            self.assertEqual(conj_ids, set(result1[1]))

    def test__assert_mergeable_rules(self):
        self.assertRaises(RuntimeError,
                          rules._assert_mergeable_rules,
                          [({'direction': 'ingress', 'ethertype': 'IPv4',
                             'protocol': 1}, 8),
                           ({'direction': 'ingress', 'ethertype': 'IPv6'},
                            16)])

    def test_merge_common_rules_single(self):
        rule_conj_tuple = ({'direction': 'egress', 'ethertype': 'IPv4',
                            'protocol': 1}, 8)
        result = rules.merge_common_rules([rule_conj_tuple])
        self.assertEqual([(rule_conj_tuple[0], [rule_conj_tuple[1]])],
                         result)

    def test_merge_common_rules(self):
        rule_conj_list = [({'direction': 'ingress', 'ethertype': 'IPv4',
                            'protocol': 1}, 8),
                          ({'direction': 'ingress', 'ethertype': 'IPv4',
                            'protocol': 1, 'port_range_min': 3}, 16),
                          ({'direction': 'ingress', 'ethertype': 'IPv4',
                            'protocol': 1, 'port_range_min': 3,
                            'port_range_max': 0}, 40),
                          ({'direction': 'ingress', 'ethertype': 'IPv4',
                            'protocol': 1}, 24)]
        result = rules.merge_common_rules(rule_conj_list)
        self.assertItemsEqual(
            [({'direction': 'ingress', 'ethertype': 'IPv4',
               'protocol': 1}, [8, 24]),
             ({'direction': 'ingress', 'ethertype': 'IPv4',
               'protocol': 1, 'port_range_min': 3}, [16]),
             ({'direction': 'ingress', 'ethertype': 'IPv4',
               'protocol': 1, 'port_range_min': 3, 'port_range_max': 0},
              [40])],
            result)

    def test_merge_port_ranges_overlapping(self):
        result = rules.merge_port_ranges(
            [(dict([('port_range_min', 20), ('port_range_max', 30)] +
                   self.rule_tmpl), 6),
             (dict([('port_range_min', 30), ('port_range_max', 40)] +
                   self.rule_tmpl), 14),
             (dict([('port_range_min', 35), ('port_range_max', 40)] +
                   self.rule_tmpl), 22),
             (dict([('port_range_min', 20), ('port_range_max', 20)] +
                   self.rule_tmpl), 30)])
        self._test_merge_port_ranges_helper([
            # port_range_min, port_range_max, conj_ids
            (20, 20, {6, 30}),
            (21, 29, {6}),
            (30, 30, {6, 14}),
            (31, 34, {14}),
            (35, 40, {14, 22})], result)

    def test_merge_port_ranges_no_port_ranges(self):
        result = rules.merge_port_ranges(
            [(dict(self.rule_tmpl), 10),
             (dict(self.rule_tmpl), 12),
             (dict([('port_range_min', 30), ('port_range_max', 40)] +
                   self.rule_tmpl), 4)])
        self._test_merge_port_ranges_helper([
                (1, 29, {10, 12}),
                (30, 40, {10, 12, 4}),
                (41, 65535, {10, 12})], result)

    def test_merge_port_ranges_no_port_ranges_same_conj_id(self):
        result = rules.merge_port_ranges(
            [(dict(self.rule_tmpl), 10),
             (dict(self.rule_tmpl), 12),
             (dict([('port_range_min', 30), ('port_range_max', 30)] +
                   self.rule_tmpl), 10)])
        self._test_merge_port_ranges_helper([
                (None, None, {10, 12})], result)

    def test_merge_port_ranges_nonoverlapping(self):
        result = rules.merge_port_ranges(
            [(dict([('port_range_min', 30), ('port_range_max', 40)] +
                   self.rule_tmpl), 32),
             (dict([('port_range_min', 100), ('port_range_max', 140)] +
                   self.rule_tmpl), 40)])
        self._test_merge_port_ranges_helper(
            [(30, 40, {32}), (100, 140, {40})], result)


class TestFlowPriority(base.BaseTestCase):
    def test_flow_priority_offset(self):
        self.assertEqual(0,
                         rules.flow_priority_offset(
                             {'foo': 'bar',
                              'remote_group_id': 'hoge'}))
        self.assertEqual(4,
                         rules.flow_priority_offset({'foo': 'bar'}))
        self.assertEqual(5,
                         rules.flow_priority_offset(
                             {'protocol': constants.PROTO_NUM_ICMP}))
        self.assertEqual(7,
                         rules.flow_priority_offset(
                             {'protocol': constants.PROTO_NUM_TCP}))

        self.assertEqual(6,
                         rules.flow_priority_offset(
                             {'protocol': constants.PROTO_NUM_ICMP,
                              'port_range_min': 0}))
        self.assertEqual(7,
                         rules.flow_priority_offset(
                             {'protocol': constants.PROTO_NUM_IPV6_ICMP,
                              'port_range_min': 0, 'port_range_max': 0}))
