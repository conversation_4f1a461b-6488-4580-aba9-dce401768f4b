# Copyright 2015 Red Hat, Inc.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools
import os
import signal
import time

from datetime import datetime

import netaddr
from neutron_lib import constants
from oslo_log import log as logging
from oslo_utils import uuidutils

from neutron.agent.l3 import ha_router
from neutron.agent.l3 import namespaces
from neutron.agent.linux import ip_lib
from neutron.agent.linux import l3_tc_lib
from neutron.agent.linux import utils
from neutron.common import utils as common_utils
from neutron.tests import base as tests_base
from neutron.tests.common.exclusive_resources import ip_network
from neutron.tests.common import net_helpers
from neutron.tests.fullstack import base
from neutron.tests.fullstack.resources import environment
from neutron.tests.fullstack.resources import machine
from neutron.tests.unit import testlib_api

load_tests = testlib_api.module_load_tests

LOG = logging.getLogger(__name__)


class TestL3Agent(base.BaseFullStackTestCase):

    def _create_external_network_and_subnet(self, tenant_id):
        network = self.safe_client.create_network(
            tenant_id, name='public', external=True)
        cidr = self.useFixture(
            ip_network.ExclusiveIPNetwork(
                "240.0.0.0", "***************", "24")).network
        subnet = self.safe_client.create_subnet(tenant_id, network['id'], cidr)
        return network, subnet

    def block_until_port_status_active(self, port_id):
        def is_port_status_active():
            port = self.client.show_port(port_id)
            return port['port']['status'] == 'ACTIVE'
        common_utils.wait_until_true(lambda: is_port_status_active(), sleep=1)

    def _create_and_attach_subnet(
            self, tenant_id, subnet_cidr, network_id, router_id):
        subnet = self.safe_client.create_subnet(
            tenant_id, network_id, subnet_cidr)

        router_interface_info = self.safe_client.add_router_interface(
            router_id, subnet['id'])
        self.block_until_port_status_active(
            router_interface_info['port_id'])

    def _boot_fake_vm_in_network(self, host, tenant_id, network_id, wait=True):
        vm = self.useFixture(
            machine.FakeFullstackMachine(
                host, network_id, tenant_id, self.safe_client,
                use_dhcp=self.use_dhcp))
        if wait:
            vm.block_until_boot()
        return vm

    def _create_net_subnet_and_vm(self, tenant_id, subnet_cidrs, host, router):
        network = self.safe_client.create_network(tenant_id)
        for cidr in subnet_cidrs:
            self._create_and_attach_subnet(
                tenant_id, cidr, network['id'], router['id'])

        return self._boot_fake_vm_in_network(host, tenant_id, network['id'])

    def _get_namespace(self, router_id, agent=None):
        namespace = namespaces.build_ns_name(namespaces.NS_PREFIX, router_id)
        if agent:
            suffix = agent.get_namespace_suffix()
        else:
            suffix = self.environment.hosts[0].l3_agent.get_namespace_suffix()
        return "%s@%s" % (namespace, suffix)

    def _get_l3_agents_with_ha_state(
            self, router_id, ha_state=None):
        l3_agents = [host.agents['l3'] for host in self.environment.hosts
                     if 'l3' in host.agents]
        found_agents = []
        agents_hosting_router = self.client.list_l3_agent_hosting_routers(
            router_id)['agents']

        for agent in l3_agents:
            agent_host = agent.neutron_cfg_fixture.get_host()
            for agent_hosting_router in agents_hosting_router:
                if (agent_hosting_router['host'] == agent_host and
                        ((ha_state is None) or (
                             agent_hosting_router['ha_state'] == ha_state))):
                    found_agents.append(agent)
                    break
        return found_agents

    def _get_hosts_with_ha_state(
            self, router_id, ha_state=None):
        return [
            self.environment.get_host_by_name(agent.hostname)
            for agent in self._get_l3_agents_with_ha_state(router_id, ha_state)
        ]

    def _router_fip_qos_after_admin_state_down_up(self, ha=False):
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(tenant_id)
        external_vm = self._create_external_vm(ext_net, ext_sub)

        router = self.safe_client.create_router(tenant_id,
                                                ha=ha,
                                                external_network=ext_net['id'])

        vm = self._create_net_subnet_and_vm(
            tenant_id, ['20.0.0.0/24', '2001:db8:aaaa::/64'],
            self.environment.hosts[1], router)
        # ping external vm to test snat
        vm.block_until_ping(external_vm.ip)

        qos_policy = self.safe_client.create_qos_policy(
            tenant_id, 'fs_policy', 'Fullstack testing policy',
            shared='False', is_default='False')
        self.safe_client.create_bandwidth_limit_rule(
            tenant_id, qos_policy['id'], 1111, 2222,
            constants.INGRESS_DIRECTION)
        self.safe_client.create_bandwidth_limit_rule(
            tenant_id, qos_policy['id'], 3333, 4444,
            constants.EGRESS_DIRECTION)

        fip = self.safe_client.create_floatingip(
            tenant_id, ext_net['id'], vm.ip, vm.neutron_port['id'],
            qos_policy_id=qos_policy['id'])
        # ping floating ip from external vm
        external_vm.block_until_ping(fip['floating_ip_address'])

        self.safe_client.update_router(router['id'], admin_state_up=False)
        external_vm.block_until_no_ping(fip['floating_ip_address'])

        self.safe_client.update_router(router['id'], admin_state_up=True)
        external_vm.block_until_ping(fip['floating_ip_address'])

        if ha:
            router_agent = self._get_l3_agents_with_ha_state(router['id'])[0]
            qrouter_ns = self._get_namespace(
                            router['id'],
                            router_agent)
        else:
            qrouter_ns = self._get_namespace(router['id'])
        ip = ip_lib.IPWrapper(qrouter_ns)
        common_utils.wait_until_true(lambda: ip.get_devices())

        devices = ip.get_devices()
        for dev in devices:
            if dev.name.startswith("qg-"):
                interface_name = dev.name

        tc_wrapper = l3_tc_lib.FloatingIPTcCommand(
            interface_name,
            namespace=qrouter_ns)
        common_utils.wait_until_true(
            functools.partial(
                self._wait_until_filters_set,
                tc_wrapper),
            timeout=60)

    def _wait_until_filters_set(self, tc_wrapper):

        def _is_filter_set(direction):
            filter_ids = tc_wrapper.get_existing_filter_ids(
                direction)
            if not filter_ids:
                return False
            return 1 == len(filter_ids)
        return (_is_filter_set(constants.INGRESS_DIRECTION) and
                _is_filter_set(constants.EGRESS_DIRECTION))


class TestLegacyL3Agent(TestL3Agent):

    # NOTE(slaweq): don't use dhcp agents due to the oslo.privsep bug
    # https://review.opendev.org/c/openstack/neutron/+/794994
    # When it will be fixed DHCP can be used here again.
    use_dhcp = False

    def setUp(self):
        host_descriptions = [
            environment.HostDescription(l3_agent=True,
                                        dhcp_agent=self.use_dhcp,
                                        l3_agent_extensions="fip_qos"),
            environment.HostDescription()]
        env = environment.Environment(
            environment.EnvironmentDescription(
                network_type='vlan', l2_pop=False,
                qos=True),
            host_descriptions)
        super(TestLegacyL3Agent, self).setUp(env)

    def test_namespace_exists(self):
        tenant_id = uuidutils.generate_uuid()

        router = self.safe_client.create_router(tenant_id)
        network = self.safe_client.create_network(tenant_id)
        subnet = self.safe_client.create_subnet(
            tenant_id, network['id'], '20.0.0.0/24', gateway_ip='********')
        self.safe_client.add_router_interface(router['id'], subnet['id'])

        namespace = self._get_namespace(router['id'])
        self.assert_namespace_exists(namespace)

    def test_mtu_update(self):
        tenant_id = uuidutils.generate_uuid()

        router = self.safe_client.create_router(tenant_id)
        network = self.safe_client.create_network(tenant_id)
        subnet = self.safe_client.create_subnet(
            tenant_id, network['id'], '20.0.0.0/24', gateway_ip='********')
        self.safe_client.add_router_interface(router['id'], subnet['id'])

        namespace = self._get_namespace(router['id'])
        self.assert_namespace_exists(namespace)

        ip = ip_lib.IPWrapper(namespace)
        common_utils.wait_until_true(lambda: ip.get_devices())

        devices = ip.get_devices()
        self.assertEqual(1, len(devices))

        ri_dev = devices[0]
        mtu = ri_dev.link.mtu
        self.assertEqual(1500, mtu)

        mtu -= 1
        network = self.safe_client.update_network(network['id'], mtu=mtu)
        common_utils.wait_until_true(lambda: ri_dev.link.mtu == mtu)

    def test_east_west_traffic(self):
        tenant_id = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id)

        vm1 = self._create_net_subnet_and_vm(
            tenant_id, ['20.0.0.0/24', '2001:db8:aaaa::/64'],
            self.environment.hosts[0], router)
        vm2 = self._create_net_subnet_and_vm(
            tenant_id, ['********/24', '2001:db8:bbbb::/64'],
            self.environment.hosts[1], router)

        vm1.block_until_ping(vm2.ip)
        # Verify ping6 from vm2 to vm1 IPv6 Address
        vm2.block_until_ping(vm1.ipv6)

    def test_north_south_traffic(self):
        # This function creates an external network which is connected to
        # central_bridge and spawns an external_vm on it.
        # The external_vm is configured with the gateway_ip (both v4 & v6
        # addresses) of external subnet. Later, it creates a tenant router,
        # a tenant network and two tenant subnets (v4 and v6). The tenant
        # router is associated with tenant network and external network to
        # provide north-south connectivity to the VMs.
        # We validate the following in this testcase.
        # 1. SNAT support: using ping from tenant VM to external_vm
        # 2. Floating IP support: using ping from external_vm to VM floating ip
        # 3. IPv6 ext connectivity: using ping6 from tenant vm to external_vm.
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(tenant_id)
        external_vm = self._create_external_vm(ext_net, ext_sub)
        # Create an IPv6 subnet in the external network
        v6network = self.useFixture(
            ip_network.ExclusiveIPNetwork(
                "2001:db8:1234::1", "2001:db8:1234::10", "64")).network
        ext_v6sub = self.safe_client.create_subnet(
            tenant_id, ext_net['id'], v6network)

        router = self.safe_client.create_router(tenant_id,
                                                external_network=ext_net['id'])

        # Configure the gateway_ip of external v6subnet on the external_vm.
        external_vm.ipv6_cidr = common_utils.ip_to_cidr(
            ext_v6sub['gateway_ip'], 64)

        # Configure an IPv6 downstream route to the v6Address of router gw port
        for fixed_ip in router['external_gateway_info']['external_fixed_ips']:
            if netaddr.IPNetwork(fixed_ip['ip_address']).version == 6:
                external_vm.set_default_gateway(fixed_ip['ip_address'])

        vm = self._create_net_subnet_and_vm(
            tenant_id, ['20.0.0.0/24', '2001:db8:aaaa::/64'],
            self.environment.hosts[1], router)

        # ping external vm to test snat
        vm.block_until_ping(external_vm.ip)

        fip = self.safe_client.create_floatingip(
            tenant_id, ext_net['id'], vm.ip, vm.neutron_port['id'])

        # ping floating ip from external vm
        external_vm.block_until_ping(fip['floating_ip_address'])

        # Verify VM is able to reach the router interface.
        vm.block_until_ping(vm.gateway_ipv6)
        fips = self.safe_client.get_floatingips()
        for fip in fips:
            if fip['floating_ip_address'] == vm.ipv6:
                self.safe_client.update_floatingip(fip['id'],
                                                   admin_state_up=True)
        # Verify north-south connectivity using ping6 to external_vm.
        vm.block_until_ping(external_vm.ipv6)

        # Now let's remove and create again phys bridge and check connectivity
        # once again
        br_phys = self.environment.hosts[0].br_phys
        br_phys.destroy()
        br_phys.create()
        self.environment.hosts[0].connect_to_central_network_via_vlans(
            br_phys)

        # ping floating ip from external vm
        external_vm.block_until_ping(fip['floating_ip_address'])

        # Verify VM is able to reach the router interface.
        vm.block_until_ping(vm.gateway_ipv6)
        # Verify north-south connectivity using ping6 to external_vm.
        vm.block_until_ping(external_vm.ipv6)

    def test_router_fip_qos_after_admin_state_down_up(self):
        self._router_fip_qos_after_admin_state_down_up()

    def test_l3_agent_restart_with_no_fullsync_connectivity(self):
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(tenant_id)
        external_vm = self._create_external_vm(ext_net, ext_sub)

        # create non-HA router and bind to external network
        router = self.safe_client.create_router(
            tenant_id, ha=False, external_network=ext_net['id'])

        # create two subnets and VMs in different subnets(test east-west)
        vm1 = self._create_net_subnet_and_vm(
            tenant_id, ['20.0.0.0/24'],
            self.environment.hosts[1], router)
        vm2 = self._create_net_subnet_and_vm(
            tenant_id, ['********/24'],
            self.environment.hosts[1], router)

        vm1.block_until_ping(vm2.ip)
        vm1.block_until_ping(external_vm.ip)

        # allocate Floating IP and test north-south connectivity
        fip = self.safe_client.create_floatingip(
            tenant_id, ext_net['id'], vm1.ip, vm1.neutron_port['id'])
        external_vm.block_until_ping(fip['floating_ip_address'])

        server = self.environment.neutron_server
        server_config = server.neutron_cfg_fixture.config
        server_config['DEFAULT']['l3_agent_down_time'] = 20
        server.neutron_cfg_fixture.write_config_to_configfile()
        server.restart()
        common_utils.wait_until_true(server.server_is_live)

        # restart L3 agent
        l3_agent_process = self.environment.hosts[0].l3_agent
        l3_agent = self.safe_client.client.list_agents(
            agent_type=constants.AGENT_TYPE_L3)['agents'][0]
        l3_agent_process.stop()
        self._wait_until_agent_down(l3_agent['id'])

        # change l3 agent configuration to avoid fullsync
        l3_agent_conf = l3_agent_process.l3_agent_cfg_fixture.config
        l3_agent_conf['agent']['restart_fullsync'] = 'False'
        l3_agent_process.l3_agent_cfg_fixture.write_config_to_configfile()

        l3_agent_process.restart()
        self._wait_until_agent_up(l3_agent['id'])
        # check if north-south and east-west connectivity is still working
        external_vm.block_until_ping(fip['floating_ip_address'])
        vm1.block_until_ping(vm2.ip)
        vm1.block_until_ping(external_vm.ip)


class TestHAL3Agent(TestL3Agent):

    # NOTE(slaweq): don't use dhcp agents due to the oslo.privsep bug
    # https://review.opendev.org/c/openstack/neutron/+/794994
    # When it will be fixed DHCP can be used here again.
    use_dhcp = False

    def setUp(self):
        # Two hosts with L3 agent to host HA routers
        host_descriptions = [
            environment.HostDescription(l3_agent=True,
                                        dhcp_agent=self.use_dhcp,
                                        l3_agent_extensions="fip_qos")
            for _ in range(2)]

        # Add two hosts for FakeFullstackMachines
        host_descriptions.extend([
            environment.HostDescription()
            for _ in range(2)
        ])

        env = environment.Environment(
            environment.EnvironmentDescription(
                network_type='vlan', l2_pop=True,
                agent_down_time=30,
                qos=True),
            host_descriptions)
        super(TestHAL3Agent, self).setUp(env)

    def _is_ha_router_active_on_one_agent(self, router_id):
        agents = self.client.list_l3_agent_hosting_routers(router_id)
        return (
            agents['agents'][0]['ha_state'] != agents['agents'][1]['ha_state'])

    def _is_ha_router_active_on_only_one_agent(self, router_id):
        agents = self.client.list_l3_agent_hosting_routers(router_id)
        return (
            agents['agents'][0]['ha_state'] == 'active')

    def test_ha_router(self):
        tenant_id = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id, ha=True)

        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 2,
            timeout=90)

        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=90)

    def _test_ha_router_failover(self, method):
        tenant_id = uuidutils.generate_uuid()

        # Create router
        router = self.safe_client.create_router(tenant_id, ha=True)
        router_id = router['id']
        agents = self.client.list_l3_agent_hosting_routers(router_id)
        self.assertEqual(2, len(agents['agents']),
                         'HA router must be scheduled to both nodes')

        # Create internal subnet1
        network1 = self.safe_client.create_network(tenant_id)
        subnet1 = self.safe_client.create_subnet(
            tenant_id, network1['id'], '20.0.0.0/24')
        self.safe_client.add_router_interface(router_id, subnet1['id'])

        # Create internal subnet2
        network2 = self.safe_client.create_network(tenant_id)
        subnet2 = self.safe_client.create_subnet(
            tenant_id, network2['id'], '30.0.0.0/24')
        self.safe_client.add_router_interface(router_id, subnet2['id'])

        # Create internal VM1
        vm1 = self.useFixture(
            machine.FakeFullstackMachine(
                self.environment.hosts[2],
                network1['id'],
                tenant_id,
                self.safe_client))
        vm1.block_until_boot()

        # Create internal VM2
        vm2 = self.useFixture(
            machine.FakeFullstackMachine(
                self.environment.hosts[3],
                network2['id'],
                tenant_id,
                self.safe_client))
        vm2.block_until_boot()

        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router_id),
            timeout=90)

        # Test tcp and udp connectivity, failover, test again
        netcat_tcp = net_helpers.NetcatTester(
            vm1.namespace,
            vm2.namespace,
            vm2.ip,
            3333,
            net_helpers.NetcatTester.TCP,
        )
        netcat_udp = net_helpers.NetcatTester(
            vm1.namespace,
            vm2.namespace,
            vm2.ip,
            3334,
            net_helpers.NetcatTester.UDP,
        )

        # Ensure connectivity before disconnect
        vm1.block_until_ping(vm2.ip)
        netcat_tcp.establish_connection()
        netcat_udp.establish_connection()

        get_active_hosts = functools.partial(
            self._get_hosts_with_ha_state,
            router_id,
            'active',
        )

        active_hosts = get_active_hosts()

        # Only one host should be active
        self.assertEqual(len(active_hosts), 1,
                         'More than one active HA routers')

        active_host = active_hosts[0]
        backup_host = next(
            h for h in self.environment.hosts if h != active_host)

        start = datetime.now()

        if method == 'disconnect':
            active_host.disconnect()
        elif method == 'kill':
            temp_dir = os.path.join(active_host.neutron_config.temp_dir,
                                    'ha_confs', router_id + '.pid-vrrp')
            pid = utils.execute(['pgrep', '-nf', temp_dir]).split()[0]
            os.kill(int(pid), signal.SIGTERM)
        elif method == 'shutdown':
            temp_dir = os.path.join(active_host.neutron_config.temp_dir)
            pids = utils.execute(['pgrep', '-f', temp_dir]).split()
            for pid in pids:
                os.kill(int(pid), signal.SIGKILL)

        if method != 'shutdown':
            # Ensure connectivity is shortly lost if the failover is not
            # graceful
            vm1.assert_no_ping(vm2.ip)

        LOG.debug('Connectivity lost after %s', datetime.now() - start)

        # Ensure connectivity is restored
        vm1.block_until_ping(vm2.ip)
        LOG.debug('Connectivity restored after %s', datetime.now() - start)

        # Ensure connection tracking states are synced to now active router
        netcat_tcp.test_connectivity()
        netcat_udp.test_connectivity()
        LOG.debug('Connections restored after %s', datetime.now() - start)

        # Assert the backup host got active
        timeout = self.environment.env_desc.agent_down_time * 1.2
        common_utils.wait_until_true(
            lambda: backup_host in get_active_hosts(),
            timeout=timeout,
        )
        LOG.debug('Active host asserted after %s', datetime.now() - start)

        if method in ('kill', 'shutdown'):
            # Assert the previously active host is no longer active if it was
            # killed or shutdown. In the disconnect case both hosts will stay
            # active, but one host is disconnected from the data plane.
            common_utils.wait_until_true(
                lambda: active_host not in get_active_hosts(),
                timeout=timeout,
            )
            LOG.debug('Inactive host asserted after {datetime.now() - start}')

        netcat_tcp.stop_processes()
        netcat_udp.stop_processes()

    def test_ha_router_failover_disconnect(self):
        self._test_ha_router_failover('disconnect')

    def test_ha_router_failover_keepalived_vrrp_killed(self):
        self._test_ha_router_failover('kill')

    def test_ha_router_failover_host_shutdown(self):
        self._test_ha_router_failover('shutdown')

    def _get_keepalived_state(self, keepalived_state_file):
        with open(keepalived_state_file, "r") as fd:
            return fd.read()

    def _get_state_file_for_master_agent(self, router_id):
        for host in self.environment.hosts:
            keepalived_state_file = os.path.join(
                host.neutron_config.config.DEFAULT.state_path,
                "ha_confs", router_id, "state")

            if self._get_keepalived_state(keepalived_state_file) == "master":
                return keepalived_state_file

    def _get_keepalived_conf_file(self, router_id):
        for host in self.environment.hosts:
            keepalived_conf_file = os.path.join(
                host.neutron_config.config.DEFAULT.state_path,
                "ha_confs", router_id, "keepalived.conf")

            if keepalived_conf_file:
                return keepalived_conf_file

    def test_keepalived_multiple_sighups_does_not_forfeit_mastership(self):
        """Setup a complete "Neutron stack" - both an internal and an external
           network+subnet, and a router connected to both.
        """
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(tenant_id)
        router = self.safe_client.create_router(tenant_id, ha=True,
                                                external_network=ext_net['id'])
        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 2,
            timeout=90)
        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=90)
        keepalived_state_file = self._get_state_file_for_master_agent(
            router['id'])
        self.assertIsNotNone(keepalived_state_file)
        network = self.safe_client.create_network(tenant_id)
        self._create_and_attach_subnet(
            tenant_id, '*********/24', network['id'], router['id'])

        # Create 10 fake VMs, each with a floating ip. Each floating ip
        # association should send a SIGHUP to the keepalived's parent process,
        # unless the Throttler works.
        host = self.environment.hosts[0]
        vms = [self._boot_fake_vm_in_network(host, tenant_id, network['id'],
                                             wait=False)
               for i in range(10)]
        for vm in vms:
            self.safe_client.create_floatingip(
                tenant_id, ext_net['id'], vm.ip, vm.neutron_port['id'])

        # Check that the keepalived's state file has not changed and is still
        # master. This will indicate that the Throttler works. We want to check
        # for ha_vrrp_advert_int (the default is 2 seconds), plus a bit more.
        time_to_stop = (time.time() +
                        (common_utils.DEFAULT_THROTTLER_VALUE *
                         ha_router.THROTTLER_MULTIPLIER * 1.3))
        while True:
            if time.time() > time_to_stop:
                break
            self.assertEqual(
                "master",
                self._get_keepalived_state(keepalived_state_file))

    def test_ha_router_restart_agents_no_packet_lost(self):
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(tenant_id)
        router = self.safe_client.create_router(tenant_id, ha=True,
                                                external_network=ext_net['id'])

        external_vm = self._create_external_vm(ext_net, ext_sub)

        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 2,
            timeout=90)

        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=90)

        router_ip = router['external_gateway_info'][
            'external_fixed_ips'][0]['ip_address']
        # Let's check first if connectivity from external_vm to router's
        # external gateway IP is possible before we restart agents
        external_vm.block_until_ping(router_ip)

        l3_standby_agents = self._get_l3_agents_with_ha_state(
            router['id'], 'standby')
        l3_active_agents = self._get_l3_agents_with_ha_state(
            router['id'], 'active')
        self.assertEqual(1, len(l3_active_agents))

        self._assert_ping_during_agents_restart(
            l3_standby_agents, external_vm.namespace, [router_ip], count=60)

        self._assert_ping_during_agents_restart(
            l3_active_agents, external_vm.namespace, [router_ip], count=60)

    @tests_base.unstable_test("bug 1946186")
    def test_router_fip_qos_after_admin_state_down_up(self):
        self._router_fip_qos_after_admin_state_down_up(ha=True)

    def test_create_ha_router_with_num_of_scheduled_agents(self):
        cloud_attrs = {'num_of_scheduled_agents': 1}
        tenant_id = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id, ha=True,
                                                cloud_attributes=cloud_attrs)
        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 1,
            timeout=90)
        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_only_one_agent,
                router['id']),
            timeout=90)

    def test_create_ha_router_with_ha_network_id(self):
        tenant_id_1 = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id_1, ha=True)
        ha_network_1 = self.client.list_networks()['networks'][0]
        self.assertTrue(tenant_id_1 in ha_network_1['name'])
        cloud_attrs = {'ha_network_id': ha_network_1['id']}
        tenant_id_2 = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id_2, ha=True,
                                                cloud_attributes=cloud_attrs)
        router_ha_ports = self.client.list_ports(
            device_id=router['id'],
            device_owner=constants.DEVICE_OWNER_ROUTER_HA_INTF)
        for port in router_ha_ports['ports']:
            self.assertEqual(port['network_id'], ha_network_1['id'])
        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 2,
            timeout=90)
        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=90)

    def test_create_ha_router_with_vr_id(self):
        cloud_attrs = {'vr_id': 100}
        tenant_id = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id, ha=True,
                                                cloud_attributes=cloud_attrs)
        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 2,
            timeout=90)
        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=90)
        keepalived_state_file = self._get_state_file_for_master_agent(
            router['id'])
        self.assertIsNotNone(keepalived_state_file)
        keepalived_conf_file = self._get_keepalived_conf_file(router['id'])
        keepalived_conf = self._get_keepalived_state(keepalived_conf_file)
        self.assertTrue('*************' in keepalived_conf)

    def _test_l3_agent_restart(self, agent_process, router,
                               external_vm, fip, vm_host1, vm_host2):
        # simulate agent faults
        router_id = router['id']
        l3_agents = self.client.list_l3_agent_hosting_routers(router_id)
        agent = next(
            l3_agent
            for l3_agent in l3_agents['agents']
            if l3_agent['host'] ==
            agent_process.neutron_cfg_fixture.get_host()
        )
        agent_process.stop()
        self._wait_until_agent_down(agent['id'])
        external_vm.block_until_ping(
            fip['floating_ip_address']
        )

        # restart agent with no fullsync
        agent_conf = agent_process.l3_agent_cfg_fixture.config
        agent_conf['agent']['restart_fullsync'] = 'False'
        agent_process.l3_agent_cfg_fixture.write_config_to_configfile()
        agent_process.restart()
        self._wait_until_agent_up(agent['id'])

        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=30)
        # check if north-south and east-west connectivity is still working
        external_vm.block_until_ping(fip['floating_ip_address'])
        vm_host1.block_until_ping(vm_host2.ip)
        vm_host1.block_until_ping(external_vm.ip)

    def test_l3_agent_restart_with_no_fullsync_connectivity(self):
        tenant_id = uuidutils.generate_uuid()
        ext_net, ext_sub = self._create_external_network_and_subnet(tenant_id)
        external_vm = self._create_external_vm(ext_net, ext_sub)

        # create HA router and bind to external network
        router = self.safe_client.create_router(
            tenant_id, ha=True, external_network=ext_net['id'])

        # create two subnets and VMs in different subnets(test east-west)
        vm_host1 = self._create_net_subnet_and_vm(
            tenant_id, ['**********/24'],
            self.environment.hosts[0], router)
        vm_host2 = self._create_net_subnet_and_vm(
            tenant_id, ['**********/24'],
            self.environment.hosts[1], router)

        # HA status validation
        common_utils.wait_until_true(
            lambda: len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 2,
            timeout=30)
        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=30)

        # allocate Floating IP and test north-south connectivity
        fip = self.safe_client.create_floatingip(
            tenant_id, ext_net['id'],
            vm_host1.ip, vm_host1.neutron_port['id'])
        external_vm.block_until_ping(fip['floating_ip_address'])
        vm_host1.block_until_ping(vm_host2.ip)

        # get active and standby agent
        active_agent_process = self._get_l3_agents_with_ha_state(
            router['id'], 'active')[0]
        standby_agent_process = self._get_l3_agents_with_ha_state(
            router['id'], 'standby')[0]

        server = self.environment.neutron_server
        server_config = server.neutron_cfg_fixture.config
        server_config['DEFAULT']['l3_agent_down_time'] = 20
        server.neutron_cfg_fixture.write_config_to_configfile()
        server.restart()
        common_utils.wait_until_true(server.server_is_live)

        # active agent restart
        self._test_l3_agent_restart(active_agent_process, router,
                                    external_vm, fip, vm_host1, vm_host2)
        # standby agent restart
        self._test_l3_agent_restart(standby_agent_process, router,
                                    external_vm, fip, vm_host1, vm_host2)


class TestHAWithThreeL3Agent(TestL3Agent):

    def setUp(self):
        host_descriptions = [
            environment.HostDescription(l3_agent=True,
                                        dhcp_agent=False,
                                        l3_agent_extensions="fip_qos")
            for _ in range(3)]
        env = environment.Environment(
            environment.EnvironmentDescription(
                network_type='vlan', l2_pop=True,
                qos=True),
            host_descriptions)
        super(TestHAWithThreeL3Agent, self).setUp(env)

    def _is_ha_router_active_on_one_agent(self, router_id):
        agents = self.client.list_l3_agent_hosting_routers(router_id)
        active_count = sum(1 for agent in agents['agents'] if
                           agent.get('ha_state') == 'active')
        if active_count == 1:
            return True
        else:
            return False

    def _get_keepalived_conf(self, keepalived_state_file):
        with open(keepalived_state_file, "r") as fd:
            return fd.read()

    def _get_keepalived_conf_files(self, router_id):
        keepalived_conf_files = []
        for host in self.environment.hosts:
            keepalived_conf_file = os.path.join(
                host.neutron_config.config.DEFAULT.state_path,
                "ha_confs", router_id, "keepalived.conf")
            keepalived_conf_files.append(keepalived_conf_file)
        return keepalived_conf_files

    def test_create_ha_router_with_unicast_in_keepalived_conf(self):
        tenant_id = uuidutils.generate_uuid()
        router = self.safe_client.create_router(tenant_id, ha=True)
        common_utils.wait_until_true(
            lambda:
            len(self.client.list_l3_agent_hosting_routers(
                router['id'])['agents']) == 3,
            timeout=90)
        common_utils.wait_until_true(
            functools.partial(
                self._is_ha_router_active_on_one_agent,
                router['id']),
            timeout=90)
        router_ha_ports = self.client.list_ports(
            device_id=router['id'],
            device_owner=constants.DEVICE_OWNER_ROUTER_HA_INTF)
        keepalived_conf_files = self._get_keepalived_conf_files(router['id'])
        self.assertEqual(3, len(keepalived_conf_files))
        for keepalived_conf_file in keepalived_conf_files:
            keepalived_conf = self._get_keepalived_conf(keepalived_conf_file)
            self.assertTrue('unicast_src_ip' in keepalived_conf)
            self.assertTrue('unicast_peer' in keepalived_conf)
            for port in router_ha_ports['ports']:
                self.assertTrue(port['fixed_ips'][0]['ip_address'] in
                                keepalived_conf)
