#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import httplib2
import netaddr
from neutron_lib.agent import l3_extension
from neutron_lib import constants
from neutron_lib.utils import file as file_utils
from oslo_config import cfg
from oslo_log import log as logging
from oslo_serialization import jsonutils
from oslo_utils import timeutils

from neutron.agent.common import ovs_lib
from neutron.agent.l3 import dvr_edge_ha_router
from neutron.agent.l3 import dvr_edge_router
from neutron.agent.l3 import ha_router
from neutron.agent.l3 import legacy_router
from neutron.agent.linux import utils
from neutron.common import constants as comm_consts
from neutron.common import coordination
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_consts

LOG = logging.getLogger(__name__)

FIP_METERING_FLOW_PRIO = 99


class FipMeteringAgentExtension(l3_extension.L3AgentExtension):
    ROUTER_FIPS = {}
    ROUTER_EXTERNAL_OFPORTS = {}

    @coordination.synchronized('fip-metering-router-{router_id}')
    def get_router_fips(self, router_id):
        return self.ROUTER_FIPS.get(router_id, set())

    @coordination.synchronized('fip-metering-router-{router_id}')
    def set_router_fips(self, router_id, fips):
        self.ROUTER_FIPS[router_id] = fips

    @coordination.synchronized('fip-metering-router-{router_id}')
    def remove_router_fips(self, router_id):
        self.ROUTER_FIPS.pop(router_id, None)

    @coordination.synchronized('fip-metering-router-{router_id}')
    def add_router_ext_ofport(self, router_id, ofport):
        self.ROUTER_EXTERNAL_OFPORTS[router_id] = ofport

    @coordination.synchronized('fip-metering-router-{router_id}')
    def remove_router_ext_ofport(self, router_id):
        return self.ROUTER_EXTERNAL_OFPORTS.pop(router_id, None)

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        self.int_br = self.initialize_bridge(
            ovs_lib.OVSBridge(cfg.CONF.ovs_integration_bridge))

    @staticmethod
    def initialize_bridge(int_br):
        return int_br.deferred(full_ordered=True, use_bundle=True)

    def consume_api(self, agent_api):
        self.agent_api = agent_api

    def _get_router_info(self, router_id):
        router_info = self.agent_api.get_router_info(router_id)
        if router_info:
            return router_info
        LOG.debug("Router %s is not managed by this agent. "
                  "It was possibly deleted concurrently.", router_id)

    def get_router_floating_ips(self, router_info):
        return (router_info.get_floating_ips() +
                router_info.get_port_forwarding_fips() +
                router_info.get_elastic_snat_fips())

    def process_router_fip_ovs_flows(self, router_info):
        router_id = router_info.router_id
        ori_router_fips = self.get_router_fips(router_id)
        cur_router_fips = set()
        floating_ips = self.get_router_floating_ips(router_info)
        gw_port = self.get_router_external_dev_name(router_info)
        if not gw_port:
            return
        if cfg.CONF.ovs_use_veth:
            gw_port = "tap" + gw_port[3:]
        gw_ofport = self.int_br.br.get_port_ofport(gw_port)
        if not gw_ofport or not isinstance(gw_ofport, int) or gw_ofport <= 0:
            return
        self.add_router_ext_ofport(router_id, gw_ofport)
        lvid = self.int_br.br.get_port_tag_by_name(gw_port)
        self.int_br.mod_flow(
            table=p_consts.DVR_POST_QOS_TABLE,
            priority=0,
            strict=True,
            actions="resubmit(,%s), resubmit(,%s), resubmit(,%s)" % (
                p_consts.TRANSIENT_TABLE,
                p_consts.FIP_METERING_BASE_INGRESS_TABLE,
                p_consts.FIP_METERING_EGRESS_TABLE))
        self.int_br.add_flow(
            table=p_consts.FIP_METERING_BASE_INGRESS_TABLE,
            priority=FIP_METERING_FLOW_PRIO,
            in_port=gw_ofport,
            actions="resubmit(,%s)" % p_consts.FIP_METERING_INGRESS_TABLE)
        if (isinstance(router_info, ha_router.HaRouter) and
                router_info.ha_state == 'master') or \
            isinstance(router_info, legacy_router.LegacyRouter) or \
                isinstance(router_info, dvr_edge_router.DvrEdgeRouter) or (
                isinstance(router_info, dvr_edge_ha_router.DvrEdgeHaRouter) and
                    router_info.ha_state == 'master'):
            for fip in floating_ips:
                fip_addr = fip['floating_ip_address']
                cur_router_fips.add(fip_addr)
                self.add_fip_flows(fip_addr, gw_ofport, lvid)

        for fip_addr in (ori_router_fips - cur_router_fips):
            self.delete_fip_flows(fip_addr, gw_ofport)

        self.set_router_fips(router_id, cur_router_fips)

    def add_fip_flows(self, fip_addr, gw_ofport, lvid):
        fip_net = netaddr.IPNetwork(fip_addr)
        if fip_net.version == constants.IP_VERSION_4:
            ip_in_kwargs = {'proto': 'ip',
                            'nw_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip',
                             'nw_src': fip_addr}
        else:
            ip_in_kwargs = {'proto': 'ip6',
                            'ipv6_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip6',
                             'ipv6_src': fip_addr}
        self.int_br.add_flow(table=p_consts.FIP_METERING_BASE_INGRESS_TABLE,
                             priority=FIP_METERING_FLOW_PRIO,
                             dl_vlan=lvid,
                             actions="resubmit(,%s)" %
                                     p_consts.FIP_METERING_INGRESS_TABLE,
                             **ip_in_kwargs)
        self.int_br.add_flow(
            table=p_consts.FIP_METERING_INGRESS_TABLE,
            priority=FIP_METERING_FLOW_PRIO,
            actions="drop",
            **ip_in_kwargs)

        self.int_br.add_flow(
            table=p_consts.FIP_METERING_EGRESS_TABLE,
            priority=FIP_METERING_FLOW_PRIO,
            in_port=gw_ofport,
            actions="drop",
            **ip_out_kwargs)

    def delete_fip_flows(self, fip_addr, gw_ofport):
        fip_net = netaddr.IPNetwork(fip_addr)
        if fip_net.version == constants.IP_VERSION_4:
            ip_in_kwargs = {'proto': 'ip',
                            'nw_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip',
                             'nw_src': fip_addr}
        else:
            ip_in_kwargs = {'proto': 'ip6',
                            'ipv6_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip6',
                             'ipv6_src': fip_addr}
        self.int_br.delete_flows(
            strict=True, table=p_consts.FIP_METERING_BASE_INGRESS_TABLE,
            priority=FIP_METERING_FLOW_PRIO,
            **ip_in_kwargs)
        self.int_br.delete_flows(strict=True,
                                 table=p_consts.FIP_METERING_INGRESS_TABLE,
                                 priority=FIP_METERING_FLOW_PRIO,
                                 **ip_in_kwargs)

        self.int_br.delete_flows(strict=True,
                                 table=p_consts.FIP_METERING_EGRESS_TABLE,
                                 priority=FIP_METERING_FLOW_PRIO,
                                 in_port=gw_ofport,
                                 **ip_out_kwargs),

    def get_router_external_dev_name(self, router_info):
        ex_gw_port = router_info.get_ex_gw_port()
        if not ex_gw_port:
            return
        agent_mode = router_info.agent_conf.agent_mode
        is_distributed_router = router_info.router.get('distributed')
        if agent_mode == constants.L3_AGENT_MODE_DVR:
            return
        if is_distributed_router and agent_mode == (
                constants.L3_AGENT_MODE_DVR_SNAT):
            # DVR edge (or DVR edge ha) router
            if not router_info._is_this_snat_host():
                return
            dev_name = router_info.get_snat_external_device_interface_name(
                ex_gw_port)
        else:
            # Legacy/HA router
            dev_name = router_info.get_external_device_interface_name(
                ex_gw_port)
        return dev_name

    def delete_router_fip_ovs_flows(self, router_id):
        gw_ofport = self.remove_router_ext_ofport(router_id)
        for fip_addr in self.get_router_fips(router_id):
            self.delete_fip_flows(fip_addr, gw_ofport)

    def _process_router(self, router_id):
        if cfg.CONF.is_sdn_arch or not cfg.CONF.enable_meter:
            return
        router_info = self._get_router_info(router_id)
        if not router_info:
            return
        self.dump_router_info(router_info)
        agent_mode = router_info.agent_conf.agent_mode
        if (agent_mode == comm_consts.L3_AGENT_MODE_DVR_BRIDGE):
            return
        self.process_router_fip_ovs_flows(router_info)
        self.int_br.apply_flows()
        self.notify_taihu_update_eip_flows_cache(router_info)

    def add_router(self, context, data):
        self._process_router(data['id'])

    def dump_router_info(self, router_info):
        # Write fip address list to the
        # /var/lib/neutron/metering/<router_id>/router_info
        router_info_path = utils.get_conf_file_name(
            router_info.agent_conf.router_metering_path,
            router_info.router_id,
            'router_info',
            True)
        contents = jsonutils.dumps(router_info.router)
        file_utils.replace_file(router_info_path, contents, file_mode=0o444)

    def update_router(self, context, data):
        self._process_router(data['id'])

    def delete_router(self, context, data):
        if cfg.CONF.is_sdn_arch or not cfg.CONF.enable_meter:
            return
        self.delete_router_fip_ovs_flows(data['id'])
        self.remove_router_fips(data['id'])
        utils.remove_conf_files(cfg.CONF.router_metering_path, data['id'])
        self.int_br.apply_flows()

    def ha_state_change(self, context, data):
        # Taihu directly read /var/lib/neutron/<router_id>/ha_confs/state
        self._process_router(data['router_id'])

    def notify_taihu_update_eip_flows_cache(self, router_info):
        if not router_info.agent_conf.metering_port or \
                (not router_info.agent_conf.metering_host):
            return
        start_time = timeutils.now()
        try:
            url = 'http://%(host)s:%(port)s' % {
                'host': router_info.agent_conf.metering_host,
                'port': router_info.agent_conf.metering_port}
            resp, _content = httplib2.Http().request(url, headers={
                'X-Neutron-Router-Id': router_info.router.get("id", None)})
        except Exception as e:
            msg = "Failed to connect to taihu service: %s" % e
            LOG.error(msg)
            return
        elapsed = timeutils.now() - start_time
        if elapsed < router_info.agent_conf.taihu_request_timeout:
            LOG.info("L3 agent connect to Taihu service. Wait time elapsed: "
                     "%0.3fs", elapsed)
        else:
            msg = "Timeout after L3 agent connect to Taihu service"
            LOG.error(msg)
            return
        if resp.status != 200:
            LOG.error("Failed to connect to Taihu service: %s", resp)
            return
        LOG.info("Finished floating ip flows cache update for a router %s ",
                 router_info.router.get("id"))
