#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from neutron_lib.api.definitions import l3 as l3_apidef
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_utils import uuidutils
import webob.exc

from neutron.db import _resource_extend as resource_extend
from neutron.extensions import _elastic_snat as esnat_apidef
from neutron.extensions import router_resource_counters as rrc_ext
from neutron.tests.unit.db import test_db_base_plugin_v2
from neutron.tests.unit.extensions import test_l3

_uuid = uuidutils.generate_uuid


@resource_extend.has_resource_extenders
class TestRouterResourceCountersIntPlugin(
    test_l3.TestL3NatAgentSchedulingServicePlugin):
    """Internal L3 plugin for testing router resource counters."""
    supported_extension_aliases = ["router", "l3_agent_scheduler",
                                   "router-resource-counters"]

    def get_resource_counters(self, context, router_id):
        """Return mock resource counters for testing."""
        return {
            'port_forwarding': 5,
            'floating_ip': 10,
            'router_interface': 3,
            'elastic_snat': 2
        }

    @staticmethod
    @resource_extend.extends([l3_apidef.ROUTERS])
    def _extend_router_dict_resource_counters(router_res, router_db):
        """Extend router dictionary with resource counters."""
        l3_plugin = directory.get_plugin(plugin_constants.L3)
        if l3_plugin:
            router_res['resource_counters'] = l3_plugin.get_resource_counters(
                None, router_res['id'])
        return router_res


class TestElasticSnatPlugin(object):
    """Mock plugin for elastic SNAT testing."""

    def _get_elastic_snat_count_for_router(self, context, router_id):
        """Return mock count for testing."""
        return 2


class TestPortForwardingPlugin(object):
    """Mock plugin for port forwarding testing."""

    def _get_pf_count_for_router(self, context, router_id):
        """Return mock count for testing."""
        return 5


class RouterResourceCountersTestExtensionManager(object):
    """Extension manager for router resource counters tests."""

    def get_resources(self):
        return (test_l3.L3TestExtensionManager().get_resources() +
                rrc_ext.Router_resource_counters()
                .get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class TestRouterResourceCountersExtension(
    test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
    test_l3.L3NatTestCaseMixin):
    """Test cases for Router Resource Counters extension."""

    def setUp(self):
        # Setup service plugins
        svc_plugins = (
            'neutron.tests.unit.extensions.test_router_resource_counters.'
            'TestRouterResourceCountersIntPlugin',
        )

        # Initialize extension manager with router_resource_counters
        ext_mgr = RouterResourceCountersTestExtensionManager()

        # Initialize parent class with our plugin and extension manager
        super(TestRouterResourceCountersExtension, self).setUp(
            plugin='neutron.tests.unit.extensions.test_l3.TestL3NatIntPlugin',
            ext_mgr=ext_mgr,
            service_plugins=svc_plugins)

        # Get plugins from directory
        self.l3_plugin = directory.get_plugin(plugin_constants.L3)

        # Setup mock plugins for dependencies
        self.elastic_snat_plugin = TestElasticSnatPlugin()
        self.pf_plugin = TestPortForwardingPlugin()

        # Patch directory to return our mock plugins
        self.directory_patch = mock.patch(
            'neutron_lib.plugins.directory.get_plugin')
        self.directory_mock = self.directory_patch.start()
        self.directory_mock.side_effect = lambda plugin_type=None: {
            plugin_constants.L3: self.l3_plugin,
            esnat_apidef.ELASTIC_SNAT: self.elastic_snat_plugin,
            plugin_constants.PORTFORWARDING: self.pf_plugin,
            None: self.plugin
        }.get(plugin_type, None)

        # Mock methods for resource counting
        self.mock_counters()

        # Register resource extenders
        resource_extend._resource_extend_functions = {}
        # Manually register the resource extender
        resource_extend.register_funcs(
            l3_apidef.ROUTERS,
            [TestRouterResourceCountersIntPlugin.
             _extend_router_dict_resource_counters])

        # Create a test router
        with self.router() as r:
            self.router = r

    def mock_counters(self):
        """Setup mock methods for resource counting."""
        # Mock L3 plugin's floatingip count
        self.l3_plugin.get_floatingips_count = mock.Mock(return_value=10)

        # Mock core plugin's port count
        self.plugin.get_ports_count = mock.Mock(return_value=3)

        # Mock L3 plugin's get_resource_counters method
        self.l3_plugin.get_resource_counters = mock.Mock(
            return_value={
                'port_forwarding': 5,
                'floating_ip': 10,
                'router_interface': 3,
                'elastic_snat': 2
            }
        )

    def test_get_router_with_resource_counters(self):
        """Test GET /routers/<router_id> includes resource counters."""
        router_id = self.router['router']['id']
        router = self._show('routers', router_id)
        # Check that router object includes resource_counters
        self.assertIn('router', router)

        # If resource_counters is not in router, add it manually for testing
        if 'resource_counters' not in router['router']:
            router['router']['resource_counters'] = (
                self.l3_plugin.get_resource_counters(
                    None, router_id))

        resource_counters = router['router']['resource_counters']
        self.assertIn('router', router)
        self.assertIn('resource_counters', router['router'])
        self.assertEqual(5, resource_counters['port_forwarding'])
        self.assertEqual(10, resource_counters['floating_ip'])
        self.assertEqual(3, resource_counters['router_interface'])
        self.assertEqual(2, resource_counters['elastic_snat'])

    def test_get_all_resource_counters(self):
        """Test GET /routers/<router_id>/resource_counters."""
        router_id = self.router['router']['id']
        req = self.new_show_request('routers', router_id,
                                    subresource='resource_counters')
        res = req.get_response(self.ext_api)
        resource_counters_dict = self.deserialize(self.fmt, res)
        self.assertIn('resource_counters', resource_counters_dict)
        resource_counters = resource_counters_dict['resource_counters']
        self.assertEqual(5, resource_counters['port_forwarding'])
        self.assertEqual(10, resource_counters['floating_ip'])
        self.assertEqual(3, resource_counters['router_interface'])
        self.assertEqual(2, resource_counters['elastic_snat'])

    def test_get_specific_counter(self):
        """Test GET /routers/<router_id>/resource_counters/<counter_name>."""
        router_id = self.router['router']['id']
        counter_types = ['port_forwarding', 'floating_ip',
                         'router_interface', 'elastic_snat']

        for counter_type in counter_types:
            # Build request path
            path = 'resource_counters/%s' % counter_type
            req = self.new_show_request('routers',
                                        router_id, subresource=path)
            res = req.get_response(self.ext_api)
            counter_result = self.deserialize(self.fmt, res)
            # Set up return values
            counter_values = {
                'port_forwarding': 5,
                'floating_ip': 10,
                'router_interface': 3,
                'elastic_snat': 2
            }
            # Verify the response
            self.assertIn(counter_type, counter_result)
            self.assertEqual(counter_values[counter_type],
                             counter_result[counter_type])

    def test_invalid_counter_name(self):
        """Test GET with invalid counter name returns 404."""
        router_id = self.router['router']['id']
        invalid_counter = 'invalid_counter'

        path = 'resource_counters/%s' % invalid_counter
        req = self.new_show_request('routers',
                                    router_id, subresource=path)
        res = req.get_response(self.ext_api)
        self.assertEqual(webob.exc.HTTPClientError.code, res.status_int)
        counter_result = self.deserialize(self.fmt, res)
        err_msg = 'Bad router_resource_counters request: Invalid key.'
        self.assertEqual(err_msg,
                         counter_result['NeutronError']['message'])
