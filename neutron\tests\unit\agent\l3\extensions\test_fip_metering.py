#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
import netaddr
from neutron_lib import constants as lib_const
from neutron_lib import context
from oslo_utils import uuidutils

from neutron.agent.l3 import agent as l3_agent
from neutron.agent.l3 import dvr_edge_router
from neutron.agent.l3.extensions import fip_metering
from neutron.agent.l3 import l3_agent_extension_api as l3_ext_api
from neutron.agent.l3 import legacy_router
from neutron.common import constants as n_const
from neutron.objects import elastic_snat as elastic_snat_obj
from neutron.objects import port_forwarding as pf_obj
from neutron.objects import router
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_consts
from neutron.tests.unit.agent.l3 import test_agent

_uuid = uuidutils.generate_uuid

HOSTNAME = 'testhost'


class FipMeteringAgentExtensionTestCase(
    test_agent.BasicRouterOperationsFramework):

    def setUp(self):
        super(FipMeteringAgentExtensionTestCase, self).setUp()

        self.fip_metering_ext = fip_metering.FipMeteringAgentExtension()

        self.context = context.get_admin_context()
        self.connection = mock.Mock()
        self.ext_net_id = _uuid()
        self.router_id = _uuid()
        self.floatingip_1 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='***********',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.floatingip_2 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='***********',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.floatingip_3 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='***********',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.floatingip_4 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='***********',
            fixed_ip_address='************',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.floatingip_5 = router.FloatingIP(
            context=None, id=_uuid(),
            floating_ip_address='abcd::1234',
            fixed_ip_address='abcd::1234',
            floating_network_id=self.ext_net_id,
            router_id=self.router_id,
            status='ACTIVE')
        self.portforwarding1 = pf_obj.PortForwarding(
            context=None, id=_uuid(), floatingip_id=self.floatingip_3.id,
            external_port=1111, protocol='tcp', internal_port_id=_uuid(),
            external_port_range='1111:1111',
            internal_port_range='11111:11111',
            internal_ip_address='*******', internal_port=11111,
            floating_ip_address=self.floatingip_3.floating_ip_address,
            router_id=self.floatingip_3.router_id)

        self.gateway_port_id = _uuid()

        self.elastic_snat_1 = elastic_snat_obj.ElasticSnat(
            context=None, id=_uuid(), name="elastic_snat_1",
            router_id=self.router_id,
            gateway_port_id=self.gateway_port_id,
            floatingip_id=self.floatingip_1.id,
            internal_cidrs=['*******', '*******', '*******/24'],
            floating_ip_address=self.floatingip_1.floating_ip_address)

        self.subnet_id_1 = _uuid()
        self.subnet_id_2 = _uuid()
        self.elastic_snat_2 = elastic_snat_obj.ElasticSnat(
            context=None, id=_uuid(), name="elastic_snat_2",
            router_id=self.router_id,
            gateway_port_id=self.gateway_port_id,
            floatingip_id=self.floatingip_2.id,
            subnets=[self.subnet_id_1, self.subnet_id_2],
            internal_cidrs=['*******/24', '*******/24'],
            floating_ip_address=self.floatingip_2.floating_ip_address)
        self.elastic_snats = [self.elastic_snat_1, self.elastic_snat_2]

        self.agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)
        self.ex_gw_port = {'id': self.gateway_port_id,
                           'network_id': self.ext_net_id,
                           'fixed_ips': [{'ip_address': '************'}]}
        self.fip1 = {
            'id': self.floatingip_1.id,
            'floating_ip_address': self.floatingip_1.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'host': HOSTNAME}
        self.fip2 = {
            'id': self.floatingip_2.id,
            'floating_ip_address': self.floatingip_2.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'host': HOSTNAME}
        self.fip3 = {
            'id': self.floatingip_3.id,
            'floating_ip_address': self.floatingip_3.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'host': HOSTNAME}
        self.fip4 = {
            'id': self.floatingip_4.id,
            'floating_ip_address': self.floatingip_4.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'fixed_ip_address': self.floatingip_4.fixed_ip_address,
            'port_id': _uuid(),
            'host': HOSTNAME}
        self.fip5 = {
            'id': self.floatingip_5.id,
            'floating_ip_address': self.floatingip_5.floating_ip_address,
            'floating_network_id': self.ext_net_id,
            'fixed_ip_address': self.floatingip_4.fixed_ip_address,
            'port_id': _uuid(),
            'host': HOSTNAME}
        self.router = {'id': self.router_id,
                       'gw_port': self.ex_gw_port,
                       'ha': False,
                       'distributed': False,
                       '_elastic_snat_rules': self.elastic_snats,
                       '_elastic_snat_floatingips': [self.fip1, self.fip2],
                       n_const.PORT_FORWARDING_FLOATINGIP_KEY: [self.fip3],
                       lib_const.FLOATINGIP_KEY: [self.fip4, self.fip5]}
        self.router_info = legacy_router.LegacyRouter(
            self.agent, self.router_id, self.router,
            **self.ri_kwargs)

        self.centralized_port_forwarding_fip_set = set(
            [str(self.floatingip_3.floating_ip_address) + '/32'])
        self.pf_managed_fips = [self.floatingip_3.id]
        self.router_info.fip_managed_by_port_forwardings = self.pf_managed_fips

        self.fip_managed_by_elastic_snats = [self.floatingip_1.id,
                                             self.floatingip_2.id]
        self.router_info.ex_gw_port = self.ex_gw_port
        self.router_info.fip_managed_by_elastic_snats = (
            self.fip_managed_by_elastic_snats)
        self.agent.router_info[self.router['id']] = self.router_info

        def _get_router_info(router_id):
            return self.agent.router_info.get(router_id)
        self.get_router_info = mock.patch(
            'neutron.agent.l3.l3_agent_extension_api.'
            'L3AgentExtensionAPI.get_router_info').start()
        self.get_router_info.side_effect = _get_router_info

        self.agent_api = l3_ext_api.L3AgentExtensionAPI(None, None)
        self.fip_metering_ext.consume_api(self.agent_api)
        mock.patch('neutron.agent.common.ovs_lib.OVSBridge').start()
        self.fip_metering_ext.initialize(self.connection, None)

        self.int_br = mock.Mock()
        self.int_br.br = mock.Mock()
        mock.patch.object(self.int_br.br, 'get_port_ofport',
                          return_value=1).start()
        mock.patch.object(self.int_br.br, 'get_port_tag_by_name',
                          return_value=10).start()

        self.m_add_flow = mock.patch.object(self.int_br, 'add_flow').start()
        mock.patch.object(self.int_br, 'apply_flows',).start()
        self.m_del_flow = mock.patch.object(
            self.int_br, 'delete_flows').start()
        self.fip_metering_ext.int_br = self.int_br

    def _assert_add_fip_flows(self, fip_addr, gw_ofport, lvid):
        fip_net = netaddr.IPNetwork(fip_addr)
        if fip_net.version == lib_const.IP_VERSION_4:
            ip_in_kwargs = {'proto': 'ip',
                            'nw_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip',
                             'nw_src': fip_addr}
        else:
            ip_in_kwargs = {'proto': 'ip6',
                            'ipv6_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip6',
                             'ipv6_src': fip_addr}
        self.m_add_flow.assert_has_calls(
            [mock.call(
                table=p_consts.FIP_METERING_BASE_INGRESS_TABLE,
                priority=fip_metering.FIP_METERING_FLOW_PRIO,
                in_port=gw_ofport,
                actions="resubmit(,%s)" %
                        p_consts.FIP_METERING_INGRESS_TABLE)])

        self.m_add_flow.assert_has_calls(
            [mock.call(
                 table=p_consts.FIP_METERING_BASE_INGRESS_TABLE,
                 priority=fip_metering.FIP_METERING_FLOW_PRIO,
                 dl_vlan=lvid,
                 actions=("resubmit(,%s)" %
                          p_consts.FIP_METERING_INGRESS_TABLE),
                 **ip_in_kwargs),
             mock.call(
                 table=p_consts.FIP_METERING_INGRESS_TABLE,
                 priority=fip_metering.FIP_METERING_FLOW_PRIO,
                 actions="drop", **ip_in_kwargs),
             mock.call(
                 table=p_consts.FIP_METERING_EGRESS_TABLE,
                 priority=fip_metering.FIP_METERING_FLOW_PRIO,
                 in_port=gw_ofport,
                 actions="drop", **ip_out_kwargs)])

    def test_add_update_router(self):
        self.fip_metering_ext.update_router(self.context, self.router)

        for fip in [self.fip1, self.fip2, self.fip3, self.fip4]:
            self._assert_add_fip_flows(fip['floating_ip_address'], 1, 10)

    def test_add_update_router_distribtued_and_dvr_snat(self):
        self.conf.set_override('agent_mode',
                               lib_const.L3_AGENT_MODE_DVR_SNAT)

        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)
        router_id = _uuid()
        router = {'id': router_id,
                  'gw_port': self.ex_gw_port,
                  'gw_port_host': HOSTNAME,
                  'ha': False,
                  'distributed': True,
                  '_elastic_snat_rules': self.elastic_snats,
                  '_elastic_snat_floatingips': [self.fip1, self.fip2],
                  n_const.PORT_FORWARDING_FLOATINGIP_KEY: [self.fip3],
                  lib_const.FLOATINGIP_KEY: [self.fip4, self.fip5]}

        router_info = dvr_edge_router.DvrEdgeRouter(
            HOSTNAME, agent, router_id, router,
            **self.ri_kwargs)

        router_info.fip_managed_by_port_forwardings = self.pf_managed_fips
        router_info.ex_gw_port = self.ex_gw_port
        router_info.fip_managed_by_elastic_snats = (
            self.fip_managed_by_elastic_snats)
        agent.router_info[router_id] = router_info

        with mock.patch.object(self.fip_metering_ext, "_get_router_info",
                               return_value=router_info):
            self.fip_metering_ext.update_router(self.context, router)

        for fip in [self.fip1, self.fip2, self.fip3, self.fip4]:
            self._assert_add_fip_flows(fip['floating_ip_address'], 1, 10)

    def test_add_update_router_distribtued_and_dvr_snat_not_this_host(self):
        self.conf.set_override('agent_mode',
                               lib_const.L3_AGENT_MODE_DVR_SNAT)

        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)
        router_id = _uuid()
        router = {'id': router_id,
                  'gw_port': self.ex_gw_port,
                  'gw_port_host': "fake",
                  'ha': False,
                  'distributed': True,
                  '_elastic_snat_rules': self.elastic_snats,
                  '_elastic_snat_floatingips': [self.fip1, self.fip2],
                  n_const.PORT_FORWARDING_FLOATINGIP_KEY: [self.fip3],
                  lib_const.FLOATINGIP_KEY: [self.fip4, self.fip5]}

        router_info = dvr_edge_router.DvrEdgeRouter(
            HOSTNAME, agent, router_id, router,
            **self.ri_kwargs)

        router_info.fip_managed_by_port_forwardings = self.pf_managed_fips
        router_info.ex_gw_port = self.ex_gw_port
        router_info.fip_managed_by_elastic_snats = (
            self.fip_managed_by_elastic_snats)
        agent.router_info[router_id] = router_info

        with mock.patch.object(self.fip_metering_ext, "_get_router_info",
                               return_value=router_info):
            self.fip_metering_ext.update_router(self.context, router)

        self.m_add_flow.assert_not_called()

    def test_add_update_router_dvr_bridge_mode(self):
        self.conf.set_override('agent_mode',
                               n_const.L3_AGENT_MODE_DVR_BRIDGE)
        with mock.patch.object(self.fip_metering_ext,
                               "dump_router_info") as m_dump_router_info:
            self.fip_metering_ext.add_router(self.context, self.router)
            m_dump_router_info.assert_called_once_with(self.router_info)

        self.m_add_flow.assert_not_called()

    def test_add_update_router_dvr_mode(self):
        self.conf.set_override('agent_mode',
                               lib_const.L3_AGENT_MODE_DVR)
        with mock.patch.object(self.fip_metering_ext,
                               "dump_router_info") as m_dump_router_info:
            self.fip_metering_ext.add_router(self.context, self.router)
            m_dump_router_info.assert_called_once_with(self.router_info)

        self.m_add_flow.assert_not_called()

    def test_add_update_router_no_router_info(self):
        with mock.patch.object(self.fip_metering_ext,
                               "dump_router_info") as m_dump_router_info:
            with mock.patch.object(self.fip_metering_ext.agent_api,
                                   "get_router_info",
                                   return_value=None):
                self.fip_metering_ext.add_router(self.context, self.router)
                m_dump_router_info.assert_not_called()

        self.m_add_flow.assert_not_called()

    def test_add_update_router_no_gateway(self):
        with mock.patch.object(self.fip_metering_ext,
                               "dump_router_info") as m_dump_router_info:
            with mock.patch.object(self.fip_metering_ext,
                                   "get_router_external_dev_name",
                                   return_value=None):
                self.fip_metering_ext.add_router(self.context, self.router)
                m_dump_router_info.assert_called_once_with(self.router_info)

        self.m_add_flow.assert_not_called()

    def _assert_del_fip_flows(self, fip_addr, gw_ofport):
        fip_net = netaddr.IPNetwork(fip_addr)
        if fip_net.version == lib_const.IP_VERSION_4:
            ip_in_kwargs = {'proto': 'ip',
                            'nw_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip',
                             'nw_src': fip_addr}
        else:
            ip_in_kwargs = {'proto': 'ip6',
                            'ipv6_dst': fip_addr}
            ip_out_kwargs = {'proto': 'ip6',
                             'ipv6_src': fip_addr}
        self.m_del_flow.assert_has_calls(
            [mock.call(strict=True,
                       table=p_consts.FIP_METERING_BASE_INGRESS_TABLE,
                       priority=fip_metering.FIP_METERING_FLOW_PRIO,
                       **ip_in_kwargs),
             mock.call(strict=True,
                       table=p_consts.FIP_METERING_INGRESS_TABLE,
                       priority=fip_metering.FIP_METERING_FLOW_PRIO,
                       **ip_in_kwargs),
             mock.call(strict=True,
                       table=p_consts.FIP_METERING_EGRESS_TABLE,
                       priority=fip_metering.FIP_METERING_FLOW_PRIO,
                       in_port=gw_ofport,
                       **ip_out_kwargs)])

    def test_delete_router(self):
        self.fip_metering_ext.add_router(self.context, self.router)
        self.assertEqual(
            5, len(self.fip_metering_ext.get_router_fips(self.router_id)))
        self.fip_metering_ext.delete_router(
            self.context, {"id": self.router_id})

        for fip in [self.fip1, self.fip2, self.fip3, self.fip4]:
            self._assert_del_fip_flows(fip['floating_ip_address'], 1)
