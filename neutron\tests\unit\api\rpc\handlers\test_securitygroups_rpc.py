# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
import netaddr
from neutron_lib import context
from oslo_config import cfg
from oslo_utils import uuidutils

from neutron.agent import resource_cache
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import securitygroups_rpc
from neutron import objects
from neutron.objects import network
from neutron.objects.port.extensions import allowedaddresspairs
from neutron.objects.port.extensions import port_security as psec
from neutron.objects import ports
from neutron.objects import securitygroup
from neutron.objects import subnet
from neutron.tests import base


class SecurityGroupServerRpcApiTestCase(base.BaseTestCase):

    def test_security_group_rules_for_devices(self):
        rpcapi = securitygroups_rpc.SecurityGroupServerRpcApi('fake_topic')

        with mock.patch.object(rpcapi.client, 'call') as rpc_mock,\
                mock.patch.object(rpcapi.client, 'prepare') as prepare_mock:
            prepare_mock.return_value = rpcapi.client
            rpcapi.security_group_rules_for_devices('context', ['fake_device'])

            rpc_mock.assert_called_once_with(
                    'context',
                    'security_group_rules_for_devices',
                    devices=['fake_device'])


class SGAgentRpcCallBackMixinTestCase(base.BaseTestCase):

    def setUp(self):
        super(SGAgentRpcCallBackMixinTestCase, self).setUp()
        self.rpc = securitygroups_rpc.SecurityGroupAgentRpcCallbackMixin()
        self.rpc.sg_agent = mock.Mock()

    def test_security_groups_rule_updated(self):
        self.rpc.security_groups_rule_updated(None,
                                              security_groups=['fake_sgid'])
        self.rpc.sg_agent.assert_has_calls(
            [mock.call.security_groups_rule_updated(['fake_sgid'])])

    def test_security_groups_member_updated(self):
        self.rpc.security_groups_member_updated(None,
                                                security_groups=['fake_sgid'])
        self.rpc.sg_agent.assert_has_calls(
            [mock.call.security_groups_member_updated(['fake_sgid'])])


class SecurityGroupServerAPIShimTestCase(base.BaseTestCase):

    def setUp(self):
        super(SecurityGroupServerAPIShimTestCase, self).setUp()
        objects.register_objects()
        resource_types = [resources.PORT, resources.SECURITYGROUP,
                          resources.SECURITYGROUPRULE, resources.NETWORK,
                          resources.SUBNET]
        self.rcache = resource_cache.RemoteResourceCache(resource_types)
        # prevent any server lookup attempts
        mock.patch.object(self.rcache, '_flood_cache_for_query').start()
        self.shim = securitygroups_rpc.SecurityGroupServerAPIShim(self.rcache)
        self.sg_agent = mock.Mock()
        self.shim.register_legacy_sg_notification_callbacks(self.sg_agent)
        self.ctx = context.get_admin_context()

    def _make_port_ovo(self, ip, **kwargs):
        attrs = {'id': uuidutils.generate_uuid(),
                 'network_id': uuidutils.generate_uuid(),
                 'security_group_ids': set(),
                 'device_owner': 'compute:None',
                 'allowed_address_pairs': []}
        attrs['fixed_ips'] = [ports.IPAllocation(
            port_id=attrs['id'], subnet_id=uuidutils.generate_uuid(),
            network_id=attrs['network_id'], ip_address=ip)]
        attrs.update(**kwargs)
        p = ports.Port(self.ctx, **attrs)
        self.rcache.record_resource_update(self.ctx, 'Port', p)
        return p

    def _make_network_ovo(self, **kwargs):
        attrs = {'id': uuidutils.generate_uuid()}
        attrs.update(**kwargs)
        net = network.Network(**attrs)
        self.rcache.record_resource_update(self.ctx, 'Network', net)
        return net

    def _make_subnet_ovo(self, **kwargs):
        attrs = {'id': uuidutils.generate_uuid(),
                 'network_id': uuidutils.generate_uuid()}
        attrs.update(**kwargs)
        sub = subnet.Subnet(self.ctx, **attrs)
        self.rcache.record_resource_update(self.ctx, 'Subnet', sub)
        return sub

    def _make_pf_port_ovo(self, ip, pf_ip, pf_net_id, pf_subnet_id, **kwargs):
        attrs = {'id': uuidutils.generate_uuid(),
                 'network_id': uuidutils.generate_uuid(),
                 'security_group_ids': set(),
                 'device_owner': 'compute:None',
                 'allowed_address_pairs': []}
        attrs['fixed_ips'] = [ports.IPAllocation(
            port_id=attrs['id'], subnet_id=uuidutils.generate_uuid(),
            network_id=attrs['network_id'], ip_address=ip),
            ports.IPAllocation(
                port_id=attrs['id'], subnet_id=pf_subnet_id,
                network_id=pf_net_id, ip_address=pf_ip)]
        attrs.update(**kwargs)
        p = ports.Port(self.ctx, **attrs)
        self.rcache.record_resource_update(self.ctx, 'Port', p)
        return p

    def _make_security_group_ovo(self, **kwargs):
        attrs = {'id': uuidutils.generate_uuid(), 'revision_number': 1}
        sg_rule = securitygroup.SecurityGroupRule(
            id=uuidutils.generate_uuid(),
            security_group_id=attrs['id'],
            direction='ingress',
            ethertype='IPv4', protocol='tcp',
            port_range_min=400,
            remote_group_id=attrs['id'],
            revision_number=1,
        )
        attrs['rules'] = [sg_rule]
        attrs.update(**kwargs)
        sg = securitygroup.SecurityGroup(self.ctx, **attrs)
        self.rcache.record_resource_update(self.ctx, 'SecurityGroup', sg)
        return sg

    def test_sg_parent_ops_affect_rules(self):
        s1 = self._make_security_group_ovo()
        filters = {'security_group_id': (s1.id, )}
        self.assertEqual(
            s1.rules,
            self.rcache.get_resources('SecurityGroupRule', filters))
        self.sg_agent.security_groups_rule_updated.assert_called_once_with(
            [s1.id])
        self.sg_agent.security_groups_rule_updated.reset_mock()
        self.rcache.record_resource_delete(self.ctx, 'SecurityGroup', s1.id)
        self.assertEqual(
            [],
            self.rcache.get_resources('SecurityGroupRule', filters))
        self.sg_agent.security_groups_rule_updated.assert_called_once_with(
            [s1.id])

    def test_security_group_info_for_devices(self):
        s1 = self._make_security_group_ovo()
        mac_1 = 'fa:16:3e:aa:bb:c1'
        p1 = self._make_port_ovo(ip='*******',
                                 mac_address=netaddr.EUI(mac_1),
                                 security_group_ids={s1.id})
        mac_2 = 'fa:16:3e:aa:bb:c2'
        p2 = self._make_port_ovo(
            ip='*******',
            mac_address=netaddr.EUI(mac_2),
            security_group_ids={s1.id},
            security=psec.PortSecurity(port_security_enabled=False))
        mac_3 = 'fa:16:3e:aa:bb:c3'
        p3 = self._make_port_ovo(ip='*******',
                                 mac_address=netaddr.EUI(mac_3),
                                 security_group_ids={s1.id},
                                 device_owner='network:dhcp')

        ids = [p1.id, p2.id, p3.id]
        info = self.shim.security_group_info_for_devices(self.ctx, ids)
        self.assertIn(('*******', str(netaddr.EUI(mac_1))),
                      info['sg_member_ips'][s1.id]['IPv4'])
        self.assertIn(('*******', str(netaddr.EUI(mac_2))),
                      info['sg_member_ips'][s1.id]['IPv4'])
        self.assertIn(('*******', str(netaddr.EUI(mac_3))),
                      info['sg_member_ips'][s1.id]['IPv4'])
        self.assertIn(p1.id, info['devices'].keys())
        self.assertIn(p2.id, info['devices'].keys())
        # P3 is a trusted port so it doesn't have rules
        self.assertIn(p3.id, info['devices'].keys())
        self.assertEqual([s1.id], list(info['security_groups'].keys()))
        self.assertTrue(info['devices'][p1.id]['port_security_enabled'])
        self.assertFalse(info['devices'][p2.id]['port_security_enabled'])
        self.assertFalse(info['devices'][p2.id]['port_security_enabled'])

    def _test_select_ips_for_remote_group(self, secgroup_id, port_ip,
                                          port_mac, ip, mac):
        allowed_pair = allowedaddresspairs.AllowedAddressPair(
            ip_address=netaddr.IPNetwork(ip),
            mac_address=netaddr.EUI(mac))
        self._make_port_ovo(port_ip, security_group_ids={secgroup_id},
                            allowed_address_pairs=[allowed_pair],
                            mac_address=netaddr.EUI(port_mac))
        cfg.CONF.set_override('exclude_privatefloating_ips', False,
                              group='SECURITYGROUP')
        return self.shim._select_ips_for_remote_group(self.ctx, {secgroup_id})

    def test_select_ips_for_remote_group_without_source_ip_check(self):
        s1 = self._make_security_group_ovo()

        port_ip = '10.0.0.0'
        port_mac = 'fa:16:3e:80:94:a7'
        ip = '***************/32'
        mac = 'fa:16:3e:e0:3a:1e'

        result = self._test_select_ips_for_remote_group(s1.id, port_ip,
                                                        port_mac, ip, mac)
        self.assertIn((port_ip, str(netaddr.EUI(port_mac))), result.get(s1.id))
        self.assertIn((ip, str(netaddr.EUI(mac))), result.get(s1.id))

    def test_select_ips_for_remote_group_with_default_source_ip_check(self):
        s1 = self._make_security_group_ovo()

        port_ip = '10.0.0.0'
        port_mac = 'fa:16:3e:80:94:a7'
        ip = '*******/0'
        mac = 'fa:16:3e:e0:3a:1e'

        result = self._test_select_ips_for_remote_group(s1.id, port_ip,
                                                        port_mac, ip, mac)
        self.assertIn((port_ip, str(netaddr.EUI(port_mac))), result.get(s1.id))
        self.assertNotIn((ip, str(netaddr.EUI(mac))), result.get(s1.id))

    def test_select_ips_for_remote_group_with_specified_source_ip_check(self):
        s1 = self._make_security_group_ovo()

        port_ip = '10.0.0.0'
        port_mac = 'fa:16:3e:80:94:a7'
        ip = '*******/0'
        mac = 'fa:16:3e:e0:3a:1e'

        cfg.CONF.set_override('source_ip_check_cidr', ip,
                              group='SECURITYGROUP')

        result = self._test_select_ips_for_remote_group(s1.id, port_ip,
                                                        port_mac, ip, mac)
        self.assertIn((port_ip, str(netaddr.EUI(port_mac))), result.get(s1.id))
        self.assertNotIn((ip, str(netaddr.EUI(mac))), result.get(s1.id))

    def _test_security_group_info_for_devices_with_privatefloating_ip(self):
        s1 = self._make_security_group_ovo()
        pf_net = self._make_network_ovo(name='privatefloating-net')
        pf_subnet = self._make_subnet_ovo(network_id=pf_net.id)
        mac_1 = 'fa:16:3e:aa:bb:c1'
        p1 = self._make_pf_port_ovo(ip='*******',
                                    pf_ip='*********',
                                    pf_net_id=pf_net.id,
                                    pf_subnet_id=pf_subnet.id,
                                    mac_address=netaddr.EUI(mac_1),
                                    security_group_ids={s1.id})
        ids = [p1.id]
        info = self.shim.security_group_info_for_devices(self.ctx, ids)
        self.assertIn(('*******', str(netaddr.EUI(mac_1))),
                      info['sg_member_ips'][s1.id]['IPv4'])
        if cfg.CONF.SECURITYGROUP.exclude_privatefloating_ips:
            self.assertNotIn(('*********', str(netaddr.EUI(mac_1))),
                             info['sg_member_ips'][s1.id]['IPv4'])
        else:
            self.assertIn(('*********', str(netaddr.EUI(mac_1))),
                          info['sg_member_ips'][s1.id]['IPv4'])

    def test_security_group_info_for_devices_include_privatefloating_ip(self):
        cfg.CONF.set_override('exclude_privatefloating_ips', False,
                              group='SECURITYGROUP')
        self._test_security_group_info_for_devices_with_privatefloating_ip()

    def test_security_group_info_for_devices_exclude_privatefloating_ip(self):
        cfg.CONF.set_override('exclude_privatefloating_ips', True,
                              group='SECURITYGROUP')
        self._test_security_group_info_for_devices_with_privatefloating_ip()

    def test_sg_member_update_events(self):
        s1 = self._make_security_group_ovo()
        p1 = self._make_port_ovo(ip='*******', security_group_ids={s1.id})
        self._make_port_ovo(ip='*******', security_group_ids={s1.id})
        self.sg_agent.security_groups_member_updated.assert_called_with(
            {s1.id})
        self.sg_agent.security_groups_member_updated.reset_mock()
        self.rcache.record_resource_delete(self.ctx, 'Port', p1.id)
        self.sg_agent.security_groups_member_updated.assert_called_with(
            {s1.id})
