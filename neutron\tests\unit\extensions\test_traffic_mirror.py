#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from neutron_lib import context
from neutron_lib.plugins import directory
from neutron_lib.plugins.ml2 import api
from oslo_config import cfg
from oslo_utils import uuidutils

from neutron.db import api as db_api
from neutron.extensions import _traffic_mirror as api_def
from neutron.extensions import traffic_mirror as traffic_mirror_ext
from neutron.objects.plugins.ml2 import vxlanallocation as vxlan_alloc_obj
from neutron.services.traffic_mirror.common import exceptions as tm_exc
from neutron.services.traffic_mirror import plugin
from neutron.tests.unit.db import test_db_base_plugin_v2
from neutron.objects import traffic_mirror
from neutron.common import utils

_uuid = uuidutils.generate_uuid


class TestTrafficMirrorPlugin(plugin.TrafficMirrorPlugin):
    supported_extension_aliases = [api_def.ALIAS]


class TestTrafficMirrorExtensionManager(object):
    def get_resources(self):
        return traffic_mirror_ext.Traffic_mirror.get_resources()

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class TestTrafficMirrorExtension(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase):
    def setUp(self):
        cfg.CONF.set_override('vni_ranges', '1:20', group='ml2_type_vxlan')

        svc_plugins = ('neutron.tests.unit.extensions.test_traffic_mirror.'
                       'TestTrafficMirrorPlugin',)
        ext_mgr = TestTrafficMirrorExtensionManager()
        super(TestTrafficMirrorExtension, self).setUp(
            plugin='neutron.plugins.ml2.plugin.Ml2Plugin',
            ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.tm_plugin = directory.get_plugin(api_def.TRAFFIC_MIRROR)
        self.tm_plugin.push_api = mock.Mock()

        self.ctx = context.get_admin_context()
        self.tennat_id = _uuid()
        self.tmf = self._create_traffic_mirror_filter()
        self.ingress_rule_1 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'src_cidr': '***********/24',
                'dst_cidr': '***********/24',
                'src_port_range': '1024:2048',
                'dst_port_range': '1024:2048',
                'protocol': 'all',
                'direction': 'ingress',
                'action': 'accept', 'priority': 90}
        self.ingress_rule_2 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'src_cidr': '***********/24',
                'direction': 'ingress',
                'action': 'reject', 'priority': 30}
        self.egress_rule_1 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'direction': 'egress',
                'action': 'accept', 'priority': 90}
        self.egress_rule_2 = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'dst_cidr': '***********/24',
                'dst_port_range': '1024:2048',
                'ethertype': 'IPv4',
                'direction': 'egress',
                'action': 'reject', 'priority': 50}

    def _create_traffic_mirror_filter(self, name=None):
        name = name or 'test_filter'
        traffic_filter = {api_def.TRAFFIC_MIRROR_FILTER: {
            'project_id': self.tennat_id,
            'name': name
        }}
        return self.tm_plugin.create_traffic_mirror_filter(self.ctx,
                                                           traffic_filter)

    def _create_traffic_mirror_filter_rule(self,
                                           traffic_mirror_filter_id=None,
                                           ethertype='IPv4',
                                           direction='ingress',
                                           action='accept',
                                           priority=10,
                                           **kwargs):
        traffic_mirror_filter_id = traffic_mirror_filter_id or self.tmf['id']
        rule = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'project_id': self.tennat_id,
            'traffic_mirror_filter_id': traffic_mirror_filter_id,
            'ethertype': ethertype,
            'direction': direction,
            'action': action,
            'priority': priority,
        }}
        rule[api_def.TRAFFIC_MIRROR_FILTER_RULE].update(**kwargs)
        return self.tm_plugin.create_traffic_mirror_filter_rule(self.ctx, rule)

    def _create_traffic_mirror_session(self, name=None,
                                       filter_id=None,
                                       sources=None,
                                       target_port_id=None,
                                       **kwargs):
        traffic_mirror_filter_id = filter_id or self.tmf['id']
        traffic_mirror_sources = sources or []
        traffic_mirror_target_port_id = target_port_id or _uuid()
        session = {api_def.TRAFFIC_MIRROR_SESSION: {
            'project_id': self.tennat_id,
            'name': name,
            'traffic_mirror_filter_id': traffic_mirror_filter_id,
            'traffic_mirror_sources': traffic_mirror_sources,
            'traffic_mirror_target_port_id': traffic_mirror_target_port_id,
            'traffic_mirror_target_type': 'eni',
            'priority': kwargs.get('priority', 10),
            'packet_length': kwargs.get('packet_length', 100),
            'enabled': kwargs.get('enabled', False),
            'virtual_network_id': kwargs.get('virtual_network_id', 1)
        }}
        return self.tm_plugin.create_traffic_mirror_session(self.ctx, session)

    def test_create_traffic_mirror_filter(self):
        tm_filter = self._create_traffic_mirror_filter('test_filter')
        self.assertEqual('test_filter', tm_filter['name'])
        self.assertEqual([], tm_filter['ingress_rules'])
        self.assertEqual([], tm_filter['egress_rules'])

    def test_update_traffic_mirror_filter(self):
        filter_kw = {api_def.TRAFFIC_MIRROR_FILTER: {
            'name': 'new_name',
            'description': 'new_description'
        }}
        tm_filter = self.tm_plugin.update_traffic_mirror_filter(
            self.ctx, self.tmf['id'], filter_kw)
        self.assertEqual('new_name', tm_filter['name'])
        self.assertEqual('new_description', tm_filter['description'])

    def test_get_traffic_mirror_filter(self):
        self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        tm_filter = self.tm_plugin.get_traffic_mirror_filter(
            self.ctx, self.tmf['id'])
        self.assertEqual('test_filter', tm_filter['name'])
        self.assertIsNone(tm_filter['description'])
        self.assertEqual(1, len(tm_filter['ingress_rules']))
        self.assertEqual(1, len(tm_filter['egress_rules']))

    def test_get_traffic_mirror_filters(self):
        tmf2 = self._create_traffic_mirror_filter('test_filter2')
        tmfs = self.tm_plugin.get_traffic_mirror_filters(self.ctx)
        self.assertEqual(2, len(tmfs))
        self.assertIn(self.tmf['id'], [x['id'] for x in tmfs])
        self.assertIn(tmf2['id'], [x['id'] for x in tmfs])

    def test_delete_traffic_mirror_filter(self):
        self.tm_plugin.delete_traffic_mirror_filter(self.ctx, self.tmf['id'])
        self.assertRaises(
            tm_exc.TrafficMirrorFilterNotFound,
            self.tm_plugin.get_traffic_mirror_filter,
            self.ctx, self.tmf['id'])

    def test_delete_bounded_traffic_mirror_filter(self):
        self._create_traffic_mirror_session()
        self.assertRaises(
            tm_exc.TrafficMirrorFilterInUse,
            self.tm_plugin.delete_traffic_mirror_filter,
            self.ctx, self.tmf['id'])

    def test_modify_fields_from_db_protocol_all(self):
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'ingress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.protocol = 'all'
        db_obj_mock.src_cidr = '***********/24'
        db_obj_mock.dst_cidr = '10.0.0.0/24'
        db_obj_mock.src_port_range_min = 1024
        db_obj_mock.src_port_range_max = 2048
        db_obj_mock.dst_port_range_min = 1024
        db_obj_mock.dst_port_range_max = 2048
        db_obj_mock.action = 'accept'
        db_obj_mock.priority = 90

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertIsNone(fields['protocol'])
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('***********/24', str(fields['src_cidr']))
        self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('10.0.0.0/24', str(fields['dst_cidr']))

    def test_modify_fields_from_db_src_cidr_none(self):
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'ingress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.protocol = 'tcp'
        db_obj_mock.src_cidr = None
        db_obj_mock.dst_cidr = '10.0.0.0/24'
        db_obj_mock.src_port_range_min = None
        db_obj_mock.src_port_range_max = None
        db_obj_mock.dst_port_range_min = 1024
        db_obj_mock.dst_port_range_max = 2048
        db_obj_mock.action = 'accept'
        db_obj_mock.priority = 90

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertEqual('tcp', fields['protocol'])
        self.assertIsNone(fields['src_cidr'])
        self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('10.0.0.0/24', str(fields['dst_cidr']))

    def test_modify_fields_from_db_dst_cidr_none(self):
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'ingress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.protocol = 'udp'
        db_obj_mock.src_cidr = '***********/24'
        db_obj_mock.dst_cidr = None
        db_obj_mock.src_port_range_min = 1024
        db_obj_mock.src_port_range_max = 2048
        db_obj_mock.dst_port_range_min = None
        db_obj_mock.dst_port_range_max = None
        db_obj_mock.action = 'accept'
        db_obj_mock.priority = 90

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertEqual('udp', fields['protocol'])
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('***********/24', str(fields['src_cidr']))
        self.assertIsNone(fields['dst_cidr'])

    def test_modify_fields_from_db_both_cidr_none(self):
        """Test modify_fields_from_db when both src_cidr and dst_cidr are None"""
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'egress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.protocol = 'tcp'
        db_obj_mock.src_cidr = None
        db_obj_mock.dst_cidr = None
        db_obj_mock.src_port_range_min = None
        db_obj_mock.src_port_range_max = None
        db_obj_mock.dst_port_range_min = None
        db_obj_mock.dst_port_range_max = None
        db_obj_mock.action = 'reject'
        db_obj_mock.priority = 50

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertEqual('tcp', fields['protocol'])
        self.assertIsNone(fields['src_cidr'])
        self.assertIsNone(fields['dst_cidr'])

    def test_modify_fields_from_db_protocol_none(self):
        """Test modify_fields_from_db when protocol is None"""
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'ingress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.protocol = None
        db_obj_mock.src_cidr = '10.0.0.0/8'
        db_obj_mock.dst_cidr = '**********/12'
        db_obj_mock.src_port_range_min = None
        db_obj_mock.src_port_range_max = None
        db_obj_mock.dst_port_range_min = None
        db_obj_mock.dst_port_range_max = None
        db_obj_mock.action = 'accept'
        db_obj_mock.priority = 100

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertIsNone(fields['protocol'])
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('10.0.0.0/8', str(fields['src_cidr']))
        self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('**********/12', str(fields['dst_cidr']))

    def test_modify_fields_from_db_protocol_specific_values(self):
        """Test modify_fields_from_db with specific protocol values"""
        protocols = ['tcp', 'udp', 'icmp', 'icmpv6']

        for protocol in protocols:
            with self.subTest(protocol=protocol):
                db_obj_mock = mock.Mock()
                db_obj_mock.id = _uuid()
                db_obj_mock.project_id = self.tennat_id
                db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
                db_obj_mock.direction = 'ingress'
                db_obj_mock.ethertype = 'IPv4' if protocol != 'icmpv6' else 'IPv6'
                db_obj_mock.protocol = protocol
                db_obj_mock.src_cidr = '***********/16'
                db_obj_mock.dst_cidr = '10.0.0.0/8'
                db_obj_mock.src_port_range_min = 80
                db_obj_mock.src_port_range_max = 80
                db_obj_mock.dst_port_range_min = 443
                db_obj_mock.dst_port_range_max = 443
                db_obj_mock.action = 'accept'
                db_obj_mock.priority = 75

                fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
                self.assertEqual(protocol, fields['protocol'])
                self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
                self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)

    def test_create_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self.assertEqual('accept', rule['action'])
        self.assertEqual(90, rule['priority'])
        self.assertEqual(self.tmf['id'], rule['traffic_mirror_filter_id'])
        self.assertEqual('ingress', rule['direction'])
        self.assertEqual('IPv4', rule['ethertype'])
        self.assertEqual('all', rule['protocol'])
        self.assertEqual('***********/24', rule['src_cidr'])
        self.assertEqual('***********/24', rule['dst_cidr'])
        self.assertEqual('1024:2048', rule['src_port_range'])
        self.assertEqual('1024:2048', rule['dst_port_range'])

    def test_create_traffic_mirror_filter_duplicate_rule(self):
        rule1 = self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        self.assertEqual('egress', rule1['direction'])
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleDuplicateOrConflict,
                          self._create_traffic_mirror_filter_rule,
                          **self.egress_rule_1)

    def test_create_traffic_mirror_filter_rule_exceed_limits(self):
        cfg.CONF.set_override(
            'max_ingress_rule_per_traffic_mirror_filter', 1, 'traffic_mirror')
        cfg.CONF.set_override(
            'max_egress_rule_per_traffic_mirror_filter', 1, 'traffic_mirror')
        self.tm_plugin.max_ingress_rules = 1
        self.tm_plugin.max_egress_rules = 1

        self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleLimitExceeded,
                          self._create_traffic_mirror_filter_rule,
                          **self.ingress_rule_2)

        self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleLimitExceeded,
                          self._create_traffic_mirror_filter_rule,
                          **self.egress_rule_2)

    def test_create_traffic_mirror_filter_rule_invalid(self):
        # invalid port value
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortValue,
                          self._create_traffic_mirror_filter_rule,
                          src_port_range='0:100')

        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortValue,
                          self._create_traffic_mirror_filter_rule,
                          dst_port_range='0:100')

        # invalid port range
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortRange,
                          self._create_traffic_mirror_filter_rule,
                          src_port_range='100:10')

        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalidPortRange,
                          self._create_traffic_mirror_filter_rule,
                          dst_port_range='100:10')

        # invalid cidr with ethertype
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self._create_traffic_mirror_filter_rule,
                          src_cidr='***********/24', ethertype='IPv6')

        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self._create_traffic_mirror_filter_rule,
                          dst_cidr='***********/24', ethertype='IPv6')

        # # invalid protocol with ethertype
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self._create_traffic_mirror_filter_rule,
                          protocol='icmpv6', ethertype='IPv4')

    def test_update_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'protocol': 'tcp',
            'src_port_range': '1000:3000',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual('1000:3000', rule['src_port_range'])

    def test_update_traffic_mirror_filter_rule_action(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'action': 'reject',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('reject', rule['action'])

    def test_update_traffic_mirror_filter_rule_port_range(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'src_port_range': '1000:3000',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('1000:3000', rule['src_port_range'])

        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'dst_port_range': '2000:3000',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('2000:3000', rule['dst_port_range'])

    def test_update_traffic_mirror_filter_rule_desc(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'description': '1234',
        }}
        rule = self.tm_plugin.update_traffic_mirror_filter_rule(
            self.ctx, rule['id'], update)
        self.assertEqual('1234', rule['description'])

    def test_update_traffic_mirror_filter_rule_invalid(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'protocol': 'icmpv6'}}
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleInvalid,
                          self.tm_plugin.update_traffic_mirror_filter_rule,
                          self.ctx, rule['id'], update)

    def test_update_traffic_mirror_filter_rule_duplicate(self):
        rule1 = self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        self._create_traffic_mirror_filter_rule(**self.ingress_rule_2)
        update = {api_def.TRAFFIC_MIRROR_FILTER_RULE: {
            'ethertype': 'IPv4',
            'src_cidr': '***********/24',
            'direction': 'ingress',
            'action': 'reject', 'priority': 30}
        }
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleDuplicateOrConflict,
                          self.tm_plugin.update_traffic_mirror_filter_rule,
                          self.ctx, rule1['id'], update)

    def test_get_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        result = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])
        self.assertEqual(rule['traffic_mirror_filter_id'],
                         result['traffic_mirror_filter_id'])
        self.assertEqual(rule['id'], result['id'])
        self.assertEqual(rule['action'], result['action'])
        self.assertEqual(rule['priority'], result['priority'])
        self.assertEqual(rule['protocol'], result['protocol'])
        self.assertEqual(rule['direction'], result['direction'])
        self.assertEqual(rule['src_cidr'], result['src_cidr'])
        self.assertEqual(rule['dst_cidr'], result['dst_cidr'])
        self.assertEqual(rule['src_port_range'], result['src_port_range'])
        self.assertEqual(rule['dst_port_range'], result['dst_port_range'])

    def test_get_traffic_mirror_filter_rule_with_protocol_all(self):
        """Test _get_traffic_mirror_filter_rule with protocol 'all' from database"""
        # Create a rule with protocol 'all'
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)

        # Verify the rule was created and protocol is displayed as 'all'
        result = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])
        self.assertEqual('all', result['protocol'])

        # Verify internal object handling - protocol should be None internally
        rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])
        self.assertIsNone(rule_obj.protocol)

    def test_get_traffic_mirror_filter_rule_with_none_cidrs(self):
        """Test _get_traffic_mirror_filter_rule with None CIDR values"""
        # Create a rule without CIDR specifications
        rule_data = {
            'traffic_mirror_filter_id': self.tmf['id'],
            'ethertype': 'IPv4',
            'protocol': 'tcp',
            'direction': 'egress',
            'action': 'accept',
            'priority': 80
        }
        rule = self._create_traffic_mirror_filter_rule(**rule_data)

        result = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, rule['id'])

        # CIDR fields should be converted to string representation
        self.assertIn('src_cidr', result)
        self.assertIn('dst_cidr', result)
        # When None, they should be converted to 'None' string in the dict
        self.assertEqual('None', result['src_cidr'])
        self.assertEqual('None', result['dst_cidr'])

    def test_get_traffic_mirror_filter_rule_not_found(self):
        """Test _get_traffic_mirror_filter_rule with non-existent rule ID"""
        non_existent_id = _uuid()

        # Should raise TrafficMirrorFilterRuleNotFound exception
        self.assertRaises(
            tm_exc.TrafficMirrorFilterRuleNotFound,
            self.tm_plugin._get_traffic_mirror_filter_rule,
            self.ctx, non_existent_id)

        # Public method should also raise the same exception
        self.assertRaises(
            tm_exc.TrafficMirrorFilterRuleNotFound,
            self.tm_plugin.get_traffic_mirror_filter_rule,
            self.ctx, non_existent_id)

    def test_get_traffic_mirror_filter_rule_with_various_protocols(self):
        """Test _get_traffic_mirror_filter_rule with different protocol values"""
        protocols = ['tcp', 'udp', 'icmp', 'all']

        for protocol in protocols:
            with self.subTest(protocol=protocol):
                rule_data = {
                    'traffic_mirror_filter_id': self.tmf['id'],
                    'ethertype': 'IPv4',
                    'protocol': protocol,
                    'direction': 'ingress',
                    'action': 'accept',
                    'priority': 60,
                    'src_cidr': '*************/24',
                    'dst_cidr': '*********/16'
                }
                rule = self._create_traffic_mirror_filter_rule(**rule_data)

                # Test the internal method
                rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
                    self.ctx, rule['id'])

                if protocol == 'all':
                    # Internal representation should be None for 'all'
                    self.assertIsNone(rule_obj.protocol)
                else:
                    # Other protocols should remain as-is
                    self.assertEqual(protocol, rule_obj.protocol)

                # Test the public method
                result = self.tm_plugin.get_traffic_mirror_filter_rule(
                    self.ctx, rule['id'])

                # Public API should always show the expected protocol
                self.assertEqual(protocol, result['protocol'])

                # Clean up for next iteration
                self.tm_plugin.delete_traffic_mirror_filter_rule(
                    self.ctx, rule['id'])

    def test_modify_fields_from_db_edge_cases(self):
        """Test modify_fields_from_db with edge cases and boundary conditions"""

        # Test with empty string protocol (should not be converted to None)
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'ingress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.protocol = ''  # Empty string
        db_obj_mock.src_cidr = '0.0.0.0/0'
        db_obj_mock.dst_cidr = '***************/32'
        db_obj_mock.src_port_range_min = 1
        db_obj_mock.src_port_range_max = 65535
        db_obj_mock.dst_port_range_min = 1
        db_obj_mock.dst_port_range_max = 65535
        db_obj_mock.action = 'accept'
        db_obj_mock.priority = 1

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertEqual('', fields['protocol'])  # Empty string should remain
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('0.0.0.0/0', str(fields['src_cidr']))
        self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('***************/32', str(fields['dst_cidr']))

    def test_modify_fields_from_db_case_sensitivity(self):
        """Test modify_fields_from_db with different case variations of 'all'"""

        # Test with uppercase 'ALL' (should not be converted)
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'egress'
        db_obj_mock.ethertype = 'IPv6'
        db_obj_mock.protocol = 'ALL'  # Uppercase
        db_obj_mock.src_cidr = '2001:db8::/32'
        db_obj_mock.dst_cidr = None
        db_obj_mock.src_port_range_min = None
        db_obj_mock.src_port_range_max = None
        db_obj_mock.dst_port_range_min = None
        db_obj_mock.dst_port_range_max = None
        db_obj_mock.action = 'reject'
        db_obj_mock.priority = 200

        fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)
        self.assertEqual('ALL', fields['protocol'])  # Should remain uppercase
        self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
        self.assertEqual('2001:db8::/32', str(fields['src_cidr']))
        self.assertIsNone(fields['dst_cidr'])

    def test_modify_fields_from_db_missing_fields(self):
        """Test modify_fields_from_db when some fields are missing from db_obj"""

        # Create a minimal db_obj without optional fields
        db_obj_mock = mock.Mock()
        db_obj_mock.id = _uuid()
        db_obj_mock.project_id = self.tennat_id
        db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
        db_obj_mock.direction = 'ingress'
        db_obj_mock.ethertype = 'IPv4'
        db_obj_mock.action = 'accept'
        db_obj_mock.priority = 50
        # Missing: protocol, src_cidr, dst_cidr, port ranges

        # Mock the parent class method to return minimal fields
        with mock.patch.object(
            traffic_mirror.TrafficMirrorFilterRule.__bases__[0],
            'modify_fields_from_db',
            return_value={
                'id': db_obj_mock.id,
                'project_id': db_obj_mock.project_id,
                'traffic_mirror_filter_id': db_obj_mock.traffic_mirror_filter_id,
                'direction': db_obj_mock.direction,
                'ethertype': db_obj_mock.ethertype,
                'action': db_obj_mock.action,
                'priority': db_obj_mock.priority
            }
        ):
            fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)

            # Should not crash and should not modify non-existent fields
            self.assertNotIn('protocol', fields)
            self.assertNotIn('src_cidr', fields)
            self.assertNotIn('dst_cidr', fields)
            self.assertEqual(db_obj_mock.id, fields['id'])
            self.assertEqual(db_obj_mock.direction, fields['direction'])

    def test_get_traffic_mirror_filter_rule_integration_with_protocol_all(self):
        """Integration test: create rule with 'all' protocol and verify end-to-end behavior"""

        # Create rule with protocol 'all'
        rule_data = {
            'traffic_mirror_filter_id': self.tmf['id'],
            'ethertype': 'IPv4',
            'protocol': 'all',
            'direction': 'ingress',
            'action': 'accept',
            'priority': 95,
            'src_cidr': '**********/12',
            'dst_cidr': '***********/16',
            'src_port_range': '8000:9000',
            'dst_port_range': '3000:4000'
        }

        # Create the rule
        created_rule = self._create_traffic_mirror_filter_rule(**rule_data)

        # Verify creation was successful
        self.assertEqual('all', created_rule['protocol'])

        # Test _get_traffic_mirror_filter_rule method
        rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
            self.ctx, created_rule['id'])

        # Internal object should have protocol as None
        self.assertIsNone(rule_obj.protocol)

        # Verify CIDR fields are properly converted
        self.assertIsInstance(rule_obj.src_cidr, utils.AuthenticIPNetwork)
        self.assertIsInstance(rule_obj.dst_cidr, utils.AuthenticIPNetwork)
        self.assertEqual('**********/12', str(rule_obj.src_cidr))
        self.assertEqual('***********/16', str(rule_obj.dst_cidr))

        # Test public get method
        retrieved_rule = self.tm_plugin.get_traffic_mirror_filter_rule(
            self.ctx, created_rule['id'])

        # Public API should show 'all' for protocol
        self.assertEqual('all', retrieved_rule['protocol'])
        self.assertEqual('**********/12', retrieved_rule['src_cidr'])
        self.assertEqual('***********/16', retrieved_rule['dst_cidr'])
        self.assertEqual('8000:9000', retrieved_rule['src_port_range'])
        self.assertEqual('3000:4000', retrieved_rule['dst_port_range'])

    def test_get_traffic_mirror_filter_rule_database_consistency(self):
        """Test database consistency when retrieving rules with protocol 'all'"""

        # Create multiple rules with different protocols including 'all'
        rules_data = [
            {
                'protocol': 'all',
                'direction': 'ingress',
                'priority': 10,
                'src_cidr': '10.0.0.0/8'
            },
            {
                'protocol': 'tcp',
                'direction': 'egress',
                'priority': 20,
                'dst_cidr': '***********/16'
            },
            {
                'protocol': 'udp',
                'direction': 'ingress',
                'priority': 30,
                'src_cidr': '**********/12',
                'dst_cidr': '*********/16'
            }
        ]

        created_rules = []
        for rule_data in rules_data:
            base_rule = {
                'traffic_mirror_filter_id': self.tmf['id'],
                'ethertype': 'IPv4',
                'action': 'accept'
            }
            base_rule.update(rule_data)
            created_rule = self._create_traffic_mirror_filter_rule(**base_rule)
            created_rules.append(created_rule)

        # Verify each rule can be retrieved correctly
        for i, created_rule in enumerate(created_rules):
            rule_obj = self.tm_plugin._get_traffic_mirror_filter_rule(
                self.ctx, created_rule['id'])
            retrieved_rule = self.tm_plugin.get_traffic_mirror_filter_rule(
                self.ctx, created_rule['id'])

            expected_protocol = rules_data[i]['protocol']

            if expected_protocol == 'all':
                # Internal representation should be None
                self.assertIsNone(rule_obj.protocol)
                # Public API should show 'all'
                self.assertEqual('all', retrieved_rule['protocol'])
            else:
                # Other protocols should remain unchanged
                self.assertEqual(expected_protocol, rule_obj.protocol)
                self.assertEqual(expected_protocol, retrieved_rule['protocol'])

    @mock.patch('neutron.objects.traffic_mirror.TrafficMirrorFilterRule.get_object')
    def test_get_traffic_mirror_filter_rule_database_error_handling(self, mock_get_object):
        """Test error handling in _get_traffic_mirror_filter_rule when database errors occur"""

        # Test when get_object returns None
        mock_get_object.return_value = None
        rule_id = _uuid()

        self.assertRaises(
            tm_exc.TrafficMirrorFilterRuleNotFound,
            self.tm_plugin._get_traffic_mirror_filter_rule,
            self.ctx, rule_id)

        # Verify get_object was called with correct parameters
        mock_get_object.assert_called_once_with(self.ctx, id=rule_id)

    def test_modify_fields_from_db_comprehensive_protocol_handling(self):
        """Comprehensive test for protocol field handling in modify_fields_from_db"""

        # Test various protocol values and their expected transformations
        protocol_test_cases = [
            ('all', None),      # 'all' should be converted to None
            ('tcp', 'tcp'),     # 'tcp' should remain 'tcp'
            ('udp', 'udp'),     # 'udp' should remain 'udp'
            ('icmp', 'icmp'),   # 'icmp' should remain 'icmp'
            ('icmpv6', 'icmpv6'), # 'icmpv6' should remain 'icmpv6'
            (None, None),       # None should remain None
            ('', ''),           # Empty string should remain empty string
            ('ALL', 'ALL'),     # Uppercase 'ALL' should remain unchanged
            ('All', 'All'),     # Mixed case should remain unchanged
            ('1', '1'),         # Numeric protocol should remain unchanged
            ('6', '6'),         # TCP protocol number should remain unchanged
            ('17', '17'),       # UDP protocol number should remain unchanged
        ]

        for input_protocol, expected_protocol in protocol_test_cases:
            with self.subTest(input_protocol=input_protocol, expected_protocol=expected_protocol):
                db_obj_mock = mock.Mock()
                db_obj_mock.id = _uuid()
                db_obj_mock.project_id = self.tennat_id
                db_obj_mock.traffic_mirror_filter_id = self.tmf['id']
                db_obj_mock.direction = 'ingress'
                db_obj_mock.ethertype = 'IPv4'
                db_obj_mock.protocol = input_protocol
                db_obj_mock.src_cidr = '***********/24'
                db_obj_mock.dst_cidr = '10.0.0.0/8'
                db_obj_mock.src_port_range_min = None
                db_obj_mock.src_port_range_max = None
                db_obj_mock.dst_port_range_min = None
                db_obj_mock.dst_port_range_max = None
                db_obj_mock.action = 'accept'
                db_obj_mock.priority = 50

                fields = traffic_mirror.TrafficMirrorFilterRule.modify_fields_from_db(db_obj_mock)

                if input_protocol == 'all':
                    self.assertIsNone(fields['protocol'])
                else:
                    self.assertEqual(expected_protocol, fields['protocol'])

                # Verify CIDR fields are always processed correctly
                self.assertIsInstance(fields['src_cidr'], utils.AuthenticIPNetwork)
                self.assertIsInstance(fields['dst_cidr'], utils.AuthenticIPNetwork)

    def test_get_traffic_mirror_filter_rules(self):
        rule1 = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        rule2 = self._create_traffic_mirror_filter_rule(**self.egress_rule_1)
        rules = self.tm_plugin.get_traffic_mirror_filter_rules(self.ctx)
        self.assertEqual(2, len(rules))
        self.assertIn(rule1['id'], [r['id'] for r in rules])
        self.assertIn(rule2['id'], [r['id'] for r in rules])

    def test_delete_traffic_mirror_filter_rule(self):
        rule = self._create_traffic_mirror_filter_rule(**self.ingress_rule_1)
        self.tm_plugin.delete_traffic_mirror_filter_rule(self.ctx, rule['id'])
        self.assertRaises(tm_exc.TrafficMirrorFilterRuleNotFound,
                          self.tm_plugin.get_traffic_mirror_filter_rule,
                          self.ctx, rule['id'])

    @db_api.context_manager.reader
    def _get_allocation(self, context, segment):
        return vxlan_alloc_obj.VxlanAllocation.get_object(
            context, vxlan_vni=segment[api.SEGMENTATION_ID])

    def test_create_traffic_mirror_session(self):
        sources = [_uuid() for _ in range(3)]
        target_id = _uuid()
        session = self._create_traffic_mirror_session(name='session',
                                                      filter_id=self.tmf['id'],
                                                      sources=sources,
                                                      target_port_id=target_id)
        self.assertEqual(session['name'], 'session')
        self.assertEqual(session['traffic_mirror_filter_id'], self.tmf['id'])
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])
        self.assertIsNotNone(session['segmentation_id'])

        segment = {api.NETWORK_TYPE: 'vxlan',
                   api.SEGMENTATION_ID: session['segmentation_id']}
        self.assertTrue(self._get_allocation(self.ctx, segment).allocated)

    def test_create_traffic_mirror_session_no_vni(self):
        vxlan_alloc_cls = vxlan_alloc_obj.VxlanAllocation
        segment = vxlan_alloc_cls.get_random_unallocated_segment(
            self.ctx)
        # allocate all vxlan's vni
        while segment:
            vxlan_alloc_obj.VxlanAllocation.allocate(
                self.ctx, vxlan_vni=segment['vxlan_vni'])
            segment = vxlan_alloc_cls.get_random_unallocated_segment(
                self.ctx)
        self.assertRaisesRegex(tm_exc.TrafficMirrorSessionCreateError,
                               'No free VXLAN segments available',
                               self._create_traffic_mirror_session)

    def test_create_traffic_mirror_session_exceed_limit(self):
        cfg.CONF.set_override(
            'max_source_per_traffic_mirror_session', 2, 'traffic_mirror')
        cfg.CONF.set_override(
            'max_traffic_mirror_session_per_source', 1, 'traffic_mirror')
        self.tm_plugin.max_sources = 2
        self.tm_plugin.max_sessions = 1

        # traffic session sources exceed
        sources = [_uuid() for _ in range(3)]
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self._create_traffic_mirror_session,
                          sources=sources)

        # source bound session exceed
        source_id = _uuid()
        session = self._create_traffic_mirror_session(sources=[source_id])
        self.assertIn(source_id, session['traffic_mirror_sources'])
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self._create_traffic_mirror_session,
                          sources=[source_id])

    def test_create_traffic_mirror_session_same_source_target(self):
        sources = [_uuid() for _ in range(2)]
        target = sources[0]
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self._create_traffic_mirror_session,
                          sources=sources, target_port_id=target)

    def test_create_traffic_mirror_session_conflict_source_target(self):
        sources1 = [_uuid() for _ in range(2)]
        sources2 = [_uuid() for _ in range(2)]
        target1 = _uuid()
        self._create_traffic_mirror_session(sources=sources1,
                                            target_port_id=target1)
        # target as source
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self._create_traffic_mirror_session,
                          sources=[target1], target_port_id=_uuid())
        # source as target
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self._create_traffic_mirror_session,
                          sources=sources2, target_port_id=sources1[1])

    def test_update_traffic_mirror_session_target(self):
        target_id = _uuid()
        new_target = _uuid()
        session = self._create_traffic_mirror_session(target_port_id=target_id)
        self.assertEqual(target_id, session['traffic_mirror_target_port_id'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_target_port_id': new_target}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertEqual(new_target, session['traffic_mirror_target_port_id'])

    def test_update_traffic_mirror_session_sources(self):
        sources = [_uuid() for _ in range(3)]
        new_sources = [_uuid() for _ in range(3)]
        session = self._create_traffic_mirror_session(sources=sources)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_sources': new_sources}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertItemsEqual(new_sources, session['traffic_mirror_sources'])

    def test_update_traffic_mirror_session_add_remove_sources(self):
        sources = [_uuid() for _ in range(3)]
        new_sources = sources + [_uuid() for _ in range(3)]
        session = self._create_traffic_mirror_session(sources=sources)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_sources': new_sources}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertItemsEqual(new_sources, session['traffic_mirror_sources'])

        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                 {'traffic_mirror_sources': sources}
        }
        session = self.tm_plugin.update_traffic_mirror_session(
            self.ctx, session['id'], update)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

    def test_update_traffic_mirror_session_exceed_limit(self):
        cfg.CONF.set_override(
            'max_source_per_traffic_mirror_session', 2, 'traffic_mirror')
        cfg.CONF.set_override(
            'max_traffic_mirror_session_per_source', 1, 'traffic_mirror')
        self.tm_plugin.max_sources = 2
        self.tm_plugin.max_sessions = 1

        sources = [_uuid() for _ in range(2)]
        session = self._create_traffic_mirror_session(sources=sources)
        self.assertItemsEqual(sources, session['traffic_mirror_sources'])

        # traffic session sources exceed
        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                {'traffic_mirror_sources': [_uuid() for _ in range(3)]}
        }
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

        # source bound session exceed
        new_sources = [_uuid() for _ in range(2)]
        self._create_traffic_mirror_session(sources=new_sources)
        update = {
            api_def.TRAFFIC_MIRROR_SESSION:
                {'traffic_mirror_sources': [new_sources[0]]}
        }
        self.assertRaises(tm_exc.TrafficMirrorSessionLimitExceeded,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

    def test_update_traffic_mirror_session_same_source_target(self):
        sources = [_uuid() for _ in range(2)]
        target = _uuid()
        session = self._create_traffic_mirror_session(sources=sources,
                                                      target_port_id=target)
        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_target_port_id': sources[0]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_sources': [target]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session['id'], update)

    def test_update_traffic_mirror_session_conflict_source_target(self):
        sources1 = [_uuid() for _ in range(2)]
        sources2 = [_uuid() for _ in range(2)]
        target1 = _uuid()
        target2 = _uuid()
        session1 = self._create_traffic_mirror_session(sources=sources1,
                                                       target_port_id=target1)
        self._create_traffic_mirror_session(sources=sources2,
                                            target_port_id=target2)

        # target as source
        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_sources': [target2]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session1['id'], update)
        # source as target
        update = {api_def.TRAFFIC_MIRROR_SESSION: {
            'traffic_mirror_target_port_id': sources2[0]
        }}
        self.assertRaises(tm_exc.TrafficMirrorSessionSourcesTargetConflict,
                          self.tm_plugin.update_traffic_mirror_session,
                          self.ctx, session1['id'], update)

    def test_get_traffic_mirror_session(self):
        sources = [_uuid() for _ in range(2)]
        target_id = _uuid()
        priority = 100
        virtual_network_id = 10000
        packet_length = 100
        enabled = True
        name = 'test'
        session = self._create_traffic_mirror_session(
            name=name, filter_id=self.tmf['id'],
            sources=sources, target_port_id=target_id,
            priority=priority, virtual_network_id=virtual_network_id,
            packet_length=packet_length, enabled=enabled)

        result = self.tm_plugin.get_traffic_mirror_session(
            self.ctx, session['id'])
        self.assertEqual(name, result['name'])
        self.assertEqual(self.tmf['id'], result['traffic_mirror_filter_id'])
        self.assertItemsEqual(sources, result['traffic_mirror_sources'])
        self.assertEqual(target_id, result['traffic_mirror_target_port_id'])
        self.assertEqual(priority, result['priority'])
        self.assertEqual(virtual_network_id, result['virtual_network_id'])
        self.assertEqual(packet_length, result['packet_length'])
        self.assertEqual(enabled, result['enabled'])

    def test_get_traffic_mirror_sessions(self):
        session1 = self._create_traffic_mirror_session()
        session2 = self._create_traffic_mirror_session()
        result = self.tm_plugin.get_traffic_mirror_sessions(self.ctx)
        self.assertEqual(2, len(result))
        self.assertIn(session1['id'], [s['id']for s in result])
        self.assertIn(session2['id'], [s['id']for s in result])

    def test_delete_traffic_mirror_session(self):
        session = self._create_traffic_mirror_session()
        segment = {api.NETWORK_TYPE: 'vxlan',
                   api.SEGMENTATION_ID: session['segmentation_id']}
        self.assertTrue(self._get_allocation(self.ctx, segment).allocated)

        self.tm_plugin.delete_traffic_mirror_session(self.ctx, session['id'])
        self.assertFalse(self._get_allocation(self.ctx, segment).allocated)
        self.assertRaises(tm_exc.TrafficMirrorSessionNotFound,
                          self.tm_plugin.get_traffic_mirror_session,
                          self.ctx, session['id'])
