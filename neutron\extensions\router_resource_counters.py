#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc

from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import constants as plugin_consts
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
import six

from neutron.api import extensions
from neutron.api.v2 import base
from neutron.api.v2 import resource as api_resource
from neutron.extensions import _router_resource_counters as apidef


class Router_resource_counters(
        api_extensions.APIExtensionDescriptor):
    """Router with type tag Extension."""

    api_definition = apidef

    @classmethod
    def get_resources(cls):
        parent = apidef.SUB_RESOURCE_ATTRIBUTE_MAP[
            apidef.COLLECTION_NAME]['parent']
        params = apidef.SUB_RESOURCE_ATTRIBUTE_MAP[
            apidef.COLLECTION_NAME]['parameters']

        controller = ResourceCountersController(
            apidef.COLLECTION_NAME,
            apidef.RESOURCE_NAME,
            params,
            parent=parent
        )
        controller = api_resource.Resource(
            controller, faults=faults.FAULT_MAP)
        resource = extensions.ResourceExtension(
            apidef.COLLECTION_NAME,
            controller,
            parent=parent,
            attr_map=params
        )
        return [resource]

    @classmethod
    def get_plugin_interface(cls):
        return None


@six.add_metaclass(abc.ABCMeta)
class RouterResourceCounterPluginBase(service_base.ServicePluginBase):
    """Service Plugin Base class for router resource counters."""

    @classmethod
    def get_plugin_type(cls):
        return apidef.RESOURCE_COUNTER

    def get_plugin_description(self):
        return "Router Resource Counter Service Plugin"

    @abc.abstractmethod
    def get_resource_counters(self, context, router_id):
        pass


class ResourceCountersController(base.Controller):
    """Controller for resource counters operations"""

    def __init__(self, collection, resource, params,
                 allow_bulk=False, parent=None):
        super(ResourceCountersController, self).__init__(
            None, collection, resource, params, allow_bulk, parent)
        self.l3_plugin = directory.get_plugin(plugin_consts.L3)

    def index(self, request, **kwargs):
        """Handler for GET /routers/<id>/resource_counters"""
        router_id = kwargs['router_id']
        res = self.l3_plugin.get_resource_counters(request.context, router_id)
        return {apidef.COLLECTION_NAME: res}

    def show(self, request, id, **kwargs):
        """Handler for GET /routers/<id>/resource_counters/key"""
        # id == key
        counter_types = ['port_forwarding', 'floating_ip',
                         'router_interface', 'elastic_snat']
        if id not in counter_types:
            raise n_exc.BadRequest(
                resource='router_resource_counters', msg='Invalid key')

        router_id = kwargs['router_id']
        router_resource_counters = self.l3_plugin.get_resource_counters(
            request.context, router_id)
        return {id: router_resource_counters[id]}
