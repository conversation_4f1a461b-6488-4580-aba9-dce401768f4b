# Copyright 2013 VMware, Inc.  All rights reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from oslo_config import cfg


router_resource_counters_opts = [
    cfg.BoolOpt('enable_extend_router_resource_counters_in_router',
               default=True,
               help='Enable extend router resource counters in router'),
]


def register_allowed_address_pair_opts(cfg=cfg.CONF):
    cfg.register_opts(router_resource_counters_opts)
