# Copyright (c) 2024 China Unicom Cloud Data Co.,Ltd.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import re

from neutron_lib import constants
from oslo_utils import uuidutils

from neutron.cmd.sanity import checks
from neutron.common import utils as common_utils
from neutron.tests.common import net_helpers
from neutron.tests.fullstack import base
from neutron.tests.fullstack.resources import environment
from neutron.tests.fullstack.resources import machine
from neutron.tests.unit import testlib_api

load_tests = testlib_api.module_load_tests

INGRESS_ACCEPT = 151
INGRESS_REJECT = 152
EGRESS_ACCEPT = 153
EGRESS_REJECT = 154


class OVSVersionChecker(object):
    conntrack_supported = None

    @classmethod
    def supports_ovs_firewall(cls):
        if cls.conntrack_supported is None:
            cls.conntrack_supported = checks.ovs_conntrack_supported()
        return cls.conntrack_supported


class BaseFlowLogTest(base.BaseFullStackTestCase):
    of_interface = None
    firewall_learn_hard_timeout = 15
    firewall_learn_idle_timeout = 15
    network_type = 'vlan'
    l2_pop = False
    across_sg_normal = False

    def setUp(self):
        host_descriptions = [
            environment.HostDescription(
                of_interface=self.of_interface,
                l2_agent_type=self.l2_agent_type,
                firewall_driver=self.firewall_driver,
                dhcp_agent=False) for _ in range(self.num_hosts)]
        env = environment.Environment(
            environment.EnvironmentDescription(
                network_type=self.network_type,
                l2_pop=self.l2_pop,
                across_sg_normal=self.across_sg_normal,
                explicitly_egress_direct=self.explicitly_egress_direct,
                firewall_learn_hard_timeout=self.firewall_learn_hard_timeout,
                firewall_learn_idle_timeout=self.firewall_learn_idle_timeout,
                enable_flow_log=True),
            host_descriptions)
        super(BaseFlowLogTest, self).setUp(env)

        if (self.firewall_driver == 'openvswitch' and
                not OVSVersionChecker.supports_ovs_firewall()):
            self.skipTest("Open vSwitch firewall_driver doesn't work "
                          "with this version of ovs.")

    def assert_connection(self, *args, **kwargs):
        netcat = net_helpers.NetcatTester(*args, **kwargs)

        def test_connectivity():
            try:
                return netcat.test_connectivity()
            except RuntimeError:
                return False

        try:
            common_utils.wait_until_true(test_connectivity)
        finally:
            netcat.stop_processes()

    def assert_no_connection(self, *args, **kwargs):
        netcat = net_helpers.NetcatTester(*args, **kwargs)
        try:
            common_utils.wait_until_true(netcat.test_no_connectivity)
        finally:
            netcat.stop_processes()

    def create_vm(self, tenant_id, network, host,
                  sg_id=None, flow_log_id=None):

        port = self.safe_client.create_port(
            tenant_id, network['id'],
            self.environment.hosts[host].hostname,
            security_groups=[],
            port_security_enabled=False,
            device_owner='compute:fake')

        if sg_id:
            self.safe_client.client.update_port(
                port['id'],
                body={'port': {'port_security_enabled': True,
                               'security_groups': [sg_id]}})
        if flow_log_id:
            self.safe_client.client.update_port(
                port['id'],
                body={'port': {'flow_log_id': flow_log_id}})

        vm = self.useFixture(
            machine.FakeFullstackMachine(self.environment.hosts[host],
                                         network['id'],
                                         tenant_id,
                                         self.safe_client,
                                         neutron_port=port,
                                         use_dhcp=False))
        vm.block_until_boot()
        return vm, port

    def create_flow_log(self, tenant_id, name, **kwargs):
        return self.safe_client.create_flow_log(tenant_id, name, **kwargs)

    def update_flow_log(self, flow_log_id, enabled=True):
        return self.safe_client.update_flow_log(flow_log_id, enabled=enabled)

    def bound_flow_log(self, port_id, flow_log_id):
        return self.safe_client.client.update_port(
            port_id, body={'port': {'flow_log_id': flow_log_id}})

    def clean_resources(self, ports):
        for port in ports:
            self.safe_client.client.update_port(
                port['id'], body={'port': {'flow_log_id': None,
                                           'security_groups': []}})

    @staticmethod
    def clean_learned_flows(vms):
        for vm in vms:
            vm.bridge.run_ofctl('del-flows', ['table=151'])
            vm.bridge.run_ofctl('del-flows', ['table=152'])
            vm.bridge.run_ofctl('del-flows', ['table=153'])
            vm.bridge.run_ofctl('del-flows', ['table=154'])

    @staticmethod
    def check_vm_flow_log_flows(vm, table, src_ip, dst_ip):
        def match_flow():
            pattern = r'nw_src=([\d.]+).*?nw_dst=([\d.]+)'

            flows = vm.bridge.dump_flows_for(table=table)
            flows = flows.split('\n')
            for flow in flows:
                m = re.search(pattern, flow)
                if m:
                    nw_src, nw_dst = m.group(1), m.group(2)
                    if nw_src == src_ip and nw_dst == dst_ip:
                        return True
            return False

        common_utils.wait_until_true(match_flow)

    @staticmethod
    def check_no_vm_flow_log_flows(vm, table, src_ip, dst_ip):
        def match_flow():
            pattern = r'nw_src=([\d.]+).*?nw_dst=([\d.]+)'

            flows = vm.bridge.dump_flows_for(table=table)
            flows = flows.split('\n')
            for flow in flows:
                m = re.search(pattern, flow)
                if m:
                    nw_src, nw_dst = m.group(1), m.group(2)
                    return nw_src == src_ip and nw_dst == dst_ip
            return False

        common_utils.wait_until_true(lambda: not match_flow())


class TestFlowLog(BaseFlowLogTest):
    scenarios = [
        ('ovs-stateless-openflow-native', {
            'firewall_driver': 'openvswitch_stateless',
            'of_interface': 'native',
            'l2_agent_type': constants.AGENT_TYPE_OVS,
            'num_hosts': 2,
            'explicitly_egress_direct': True})
    ]

    def _test_flow_log(self, tenant_id, vm1, vm2, port1, port2):
        """Tests if flow log are working
        case 0. traffic is allowed when no port security and no flow log
             1. binding one flow log, log only one vm flows
             2. binding two flow log, log both vm1, vm2 flows
             3. enable one port security, port w/o sg -> port w/ sg
             4. enable one port security, port w/ sg -> port w/o sg
             5. enable two port security, port w/ sg -> port w/ sg
             6. binding other flow log but not enable, log only vm1
             7. enable other flow log, log both vm1, vm2
             8. disable port1 flow log, log only vm2 flows
             9. disable port1 security, flow log, log only vm2 flows,
                port w/o sg -> port w/ sg
             10. disable port1 security, flow log, log only vm2 flows,
                 port w/ sg -> port w/o sg
        """
        sg = self.safe_client.create_security_group(tenant_id)
        flow_log = self.create_flow_log(tenant_id, 'flow-log-test',
                                        enabled=True)

        self.safe_client.create_security_group_rule(
            tenant_id, sg['id'],
            direction='ingress',
            ethertype=constants.IPv4,
            protocol=constants.PROTO_NAME_TCP,
            port_range_min=3333, port_range_max=3333)

        # case 0: no sg, no flow log
        self.assert_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3333,
            net_helpers.NetcatTester.TCP)

        # case 1: no sg -> no sg
        # enable port1 flow log, should only log vm1 flows
        self.bound_flow_log(port1['id'], flow_log['id'])
        self.assert_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_no_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 2: no sg -> no sg
        # enable port1 port2 flow log, log both vm1, vm2
        self.bound_flow_log(port2['id'], flow_log['id'])
        self.assert_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 3: port w/o sg -> port w/ sg
        # port1 bound sg, log both vm1, vm2
        self.safe_client.client.update_port(
            port1['id'],
            body={'port': {'port_security_enabled': True,
                           'security_groups': [sg['id']]}})
        self.assert_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 4: port w/ sg -> port w/o sg
        self.assert_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 5: port w/ sg -> port w/ sg
        self.safe_client.client.update_port(
            port2['id'],
            body={'port': {'port_security_enabled': True,
                           'security_groups': [sg['id']]}})
        self.assert_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 6: port w/ sg -> port w/ sg
        # port2 bound other flow log, but not enable
        flow_log2 = self.create_flow_log(tenant_id, 'flow-log2')
        self.bound_flow_log(port2['id'], flow_log2['id'])
        self.assert_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_no_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 7: port w/ sg -> port w/ sg
        # port2 enable flow log
        self.update_flow_log(flow_log2['id'], enabled=True)
        self.assert_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 8: port w/ sg -> port w/ sg
        # disable port1 flow, should only log vm2
        self.update_flow_log(flow_log['id'], enabled=False)
        self.assert_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_no_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 9: port w/o sg -> port w/ sg
        # disable port1 security, flow log, should only log vm2
        self.safe_client.client.update_port(
            port1['id'],
            body={'port': {'port_security_enabled': False,
                           'security_groups': []}})
        self.assert_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_no_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 10: port w/ sg -> port w/o sg
        # disable port1 security, flow log, should only log vm2
        self.assert_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3333,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm1, EGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm1, INGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

    def test_flow_log_on_diff_host(self):
        tenant_id = uuidutils.generate_uuid()
        subnet_cidr = '20.0.0.0/24'
        network = self.safe_client.create_network(tenant_id)
        self.safe_client.create_subnet(tenant_id, network['id'], subnet_cidr)

        vm1, port1 = self.create_vm(tenant_id, network, 0)
        vm2, port2 = self.create_vm(tenant_id, network, 1)
        self._test_flow_log(tenant_id, vm1, vm2, port1, port2)

        self.clean_resources([port1, port2])

    def test_flow_log_on_same_host(self):
        tenant_id = uuidutils.generate_uuid()
        subnet_cidr = '20.0.0.0/24'
        network = self.safe_client.create_network(tenant_id)
        self.safe_client.create_subnet(tenant_id, network['id'], subnet_cidr)

        vm1, port1 = self.create_vm(tenant_id, network, 0)
        vm2, port2 = self.create_vm(tenant_id, network, 0)
        self._test_flow_log(tenant_id, vm1, vm2, port1, port2)

        self.clean_resources([port1, port2])

    def _test_reject_flow_log(self, tenant_id, vm1, vm2, port1, port2):
        """Tests if reject flow log are working.
        case 0. traffic is allowed when no port security and no flow log
             1. port1 bound flow log, and add deny egress rule
             2. add deny ingress rule, port w/o sg -> port w/ sg
             3. port2 enable sg, port w/ sg -> port w/ sg
             4. port2 bound flow log, port w/ sg -> port w/ sg
             5. port w/o sg, w/ flow log -> port w/ sg, w/ flow log
             6. port w/o sg, w/ flow log -> port w/ sg, w/o flow log
        """

        sg = self.safe_client.create_security_group(tenant_id)
        flow_log = self.create_flow_log(tenant_id, 'flow-log-test',
                                        enabled=True)

        deny_rule = self.safe_client.create_security_group_rule(
            tenant_id, sg['id'],
            direction='egress',
            action='deny',
            ethertype=constants.IPv4,
            protocol=constants.PROTO_NAME_TCP,
            port_range_min=3344, port_range_max=3344)

        # case 0: no sg, no flow log
        self.assert_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3344,
            net_helpers.NetcatTester.TCP)

        # case 1: deny egress flow
        # port1 bound security, flow log
        self.safe_client.client.update_port(
            port1['id'],
            body={'port': {'port_security_enabled': True,
                           'security_groups': [sg['id']]}})

        self.bound_flow_log(port1['id'], flow_log['id'])
        self.assert_no_connection(
            vm1.namespace, vm2.namespace, vm2.ip, 3344,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, EGRESS_REJECT, vm1.ip, vm2.ip)
        self.check_no_vm_flow_log_flows(vm2, INGRESS_ACCEPT, vm1.ip, vm2.ip)
        self.clean_learned_flows([vm1])

        self.client.delete_security_group_rule(deny_rule['id'])

        # case 2: deny ingress flow
        # port w/o sg -> port w/ sg
        #                     w/ flow log
        self.safe_client.create_security_group_rule(
            tenant_id, sg['id'],
            direction='ingress',
            action='deny',
            ethertype=constants.IPv4,
            protocol=constants.PROTO_NAME_TCP,
            port_range_min=3344, port_range_max=3344)
        self.assert_no_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3344,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_REJECT, vm2.ip, vm1.ip)
        self.check_no_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 3: deny ingress flow
        # port w/ sg -> port w/ sg
        #                    w/ flow log
        sg2 = self.safe_client.create_security_group(tenant_id)
        self.safe_client.client.update_port(
            port2['id'],
            body={'port': {'port_security_enabled': True,
                           'security_groups': [sg2['id']]}})
        self.assert_no_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3344,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_REJECT, vm2.ip, vm1.ip)
        self.check_no_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 4: deny ingress flow
        # port w/ sg        ->  port w/ sg
        #      w/ flow log           w/ flow log
        self.bound_flow_log(port2['id'], flow_log['id'])
        self.assert_no_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3344,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_REJECT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 5: deny ingress flow
        # port w/o sg        ->  port w/ sg
        #      w/ flow log           w/ flow log
        self.safe_client.client.update_port(
            port2['id'],
            body={'port': {'port_security_enabled': False,
                           'security_groups': []}})
        self.assert_no_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3344,
            net_helpers.NetcatTester.TCP)
        self.check_vm_flow_log_flows(vm1, INGRESS_REJECT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

        # case 6: disable port1 flow log
        # port w/o sg        -> port w/sg
        #      w/ flow log           w/o flow log
        self.safe_client.client.update_port(
            port1['id'],
            body={'port': {'flow_log_id': None}})
        self.assert_no_connection(
            vm2.namespace, vm1.namespace, vm1.ip, 3344,
            net_helpers.NetcatTester.TCP)
        self.check_no_vm_flow_log_flows(vm1, INGRESS_REJECT, vm2.ip, vm1.ip)
        self.check_vm_flow_log_flows(vm2, EGRESS_ACCEPT, vm2.ip, vm1.ip)
        self.clean_learned_flows([vm1, vm2])

    def test_reject_flow_log_on_diff_host(self):
        tenant_id = uuidutils.generate_uuid()
        subnet_cidr = '20.0.0.0/24'
        network = self.safe_client.create_network(tenant_id)
        self.safe_client.create_subnet(tenant_id, network['id'], subnet_cidr)

        vm1, port1 = self.create_vm(tenant_id, network, 0)
        vm2, port2 = self.create_vm(tenant_id, network, 1)
        self._test_reject_flow_log(tenant_id, vm1, vm2, port1, port2)

        self.clean_resources([port1, port2])

    def test_reject_flow_log_on_same_host(self):
        tenant_id = uuidutils.generate_uuid()
        subnet_cidr = '20.0.0.0/24'
        network = self.safe_client.create_network(tenant_id)
        self.safe_client.create_subnet(tenant_id, network['id'], subnet_cidr)

        vm1, port1 = self.create_vm(tenant_id, network, 0)
        vm2, port2 = self.create_vm(tenant_id, network, 0)
        self._test_reject_flow_log(tenant_id, vm1, vm2, port1, port2)

        self.clean_resources([port1, port2])


class TestFlowLogOvs(TestFlowLog):
    across_sg_normal = True
    scenarios = [
        ('ovs-openflow-native', {
            'firewall_driver': 'openvswitch',
            'of_interface': 'native',
            'l2_agent_type': constants.AGENT_TYPE_OVS,
            'num_hosts': 2,
            'explicitly_egress_direct': False})
    ]


class TestFlowLogOvsWithoutSgNormal(TestFlowLog):
    scenarios = [
        ('ovs-openflow-native', {
            'firewall_driver': 'openvswitch',
            'of_interface': 'native',
            'l2_agent_type': constants.AGENT_TYPE_OVS,
            'num_hosts': 2,
            'explicitly_egress_direct': False})
    ]
