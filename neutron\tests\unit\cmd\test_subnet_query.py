# Copyright (c) 2014 OpenStack Foundation.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.

from collections import OrderedDict
import io
import sys

import mock

from neutron.tests import base
from sqlalchemy import Column
from sqlalchemy import MetaData
from sqlalchemy import String
from sqlalchemy import Table

from neutron.cmd import subnet_query as query_script


class TestQuerySubnets(base.BaseTestCase):

    def setUp(self):
        super(TestQuerySubnets, self).setUp()

        self.mock_engine = mock.MagicMock()
        self.mock_conn = mock.MagicMock()
        self.mock_engine.connect.return_value.__enter__.return_value = (
            self.mock_conn)
        self.metadata = MetaData()
        self.subnets_table = Table(
            'subnets', self.metadata,
            Column('id', String),
            Column('cidr', String),
            Column('name', String)
        )

        patcher = mock.patch('neutron.cmd.subnet_query.Table',
                             return_value=self.subnets_table)
        self.addCleanup(patcher.stop)
        patcher.start()

    def _mock_query_result(self, rows):
        mock_result = mock.MagicMock()
        mock_result.fetchall.return_value = rows
        self.mock_conn.execute.return_value = mock_result

    def _capture_query_output(self, cidr):
        buf = io.BytesIO() if sys.version_info[0] == 2 else io.StringIO()
        original_stdout = sys.stdout
        sys.stdout = buf
        try:
            query_script.query_subnets(self.mock_engine, cidr)
        finally:
            sys.stdout = original_stdout

        output = buf.getvalue()
        return output

    def test_query_subnets(self):
        rows = [
            OrderedDict([('id', 'A'), ('cidr', '*************/24'),
                         ('name', 'subnet1')]),
            OrderedDict([('id', 'B'), ('cidr', '*************/24'),
                         ('name', 'subnet2')]),
            OrderedDict([('id', 'C'), ('cidr', '10.0.0.0/24'),
                         ('name', 'subnet3')]),
        ]
        self._mock_query_result(rows)

        output = self._capture_query_output('%')

        self.assertIn('A', output)
        self.assertIn('*************/24', output)
        self.assertIn('subnet1', output)

        self.assertIn('B', output)
        self.assertIn('*************/24', output)
        self.assertIn('subnet2', output)

        self.assertIn('C', output)
        self.assertIn('10.0.0.0/24', output)
        self.assertIn('subnet3', output)

    def test_query_subnets_no_match(self):
        self._mock_query_result([])

        output = self._capture_query_output('10.0.99.%')
        self.assertIn('No subnets found matching CIDR: 10.0.99.%', output)

    def test_query_subnets_column_widths_align(self):
        rows = [
            OrderedDict([('id', '1'), ('cidr', '10.0.0.0/24'), ('name', 'x')]),
            OrderedDict([('id', '22'), ('cidr', '***********/24'),
                         ('name', 'long_subnet_name')])
        ]
        self._mock_query_result(rows)

        output = self._capture_query_output('%')
        lines = output.strip().splitlines()
        self.assertEqual(len(lines[0]), len(lines[1]))
        self.assertEqual(len(lines[1]), len(lines[2]))

    def test_query_subnets_cidr_argument_passed_correctly(self):
        cidr_value = '192.168.1.%'
        self._mock_query_result([])

        query_script.query_subnets(self.mock_engine, cidr_value)
        args, kwargs = self.mock_conn.execute.call_args
        query = args[0]
        compiled = str(query.compile(compile_kwargs={"literal_binds": False}))
        self.assertIn("LIKE", compiled)
        self.assertIn("subnets.cidr", compiled)

        self.assertEqual(query.compile().params['cidr_1'], cidr_value)

    def test_main_success_path(self):
        with mock.patch('neutron.cmd.subnet_query.setup_conf'), \
                mock.patch(
                    'neutron.cmd.subnet_query.server._get_config_files',
                    return_value=[]), \
                mock.patch(
                    'neutron.cmd.subnet_query.cfg.CONF.register_cli_opts'), \
                mock.patch('neutron.cmd.subnet_query.config.init'), \
                mock.patch(
                    'neutron.cmd.subnet_query.db_options.set_defaults'), \
                mock.patch(
                    'neutron.cmd.subnet_query.get_db_connection'
                     ) as mock_get_engine, \
                mock.patch(
                    'neutron.cmd.subnet_query.query_subnets') as mock_query:
            mock_get_engine.return_value = self.mock_engine
            mock_query.return_value = None
            with mock.patch('neutron.cmd.subnet_query.cfg.CONF') as mock_conf:
                mock_conf.cidr = '10.0.%'
                query_script.main()
                mock_query.assert_called_once_with(self.mock_engine, '10.0.%')

    def test_main_with_exception(self):
        with mock.patch('neutron.cmd.subnet_query.setup_conf',
                        side_effect=Exception("Boom")), \
                mock.patch('sys.exit') as mock_exit:
            query_script.main()
            mock_exit.assert_called_once_with(1)
