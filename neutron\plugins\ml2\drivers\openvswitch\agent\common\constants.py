# Copyright (c) 2012 OpenStack Foundation.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from neutron_lib import constants as p_const


# Special vlan_id value in ovs_vlan_allocations table indicating flat network
FLAT_VLAN_ID = -1

# Topic for tunnel notifications between the plugin and agent
TUNNEL = 'tunnel'

# Name prefixes for veth device or patch port pair linking the integration
# bridge with the physical bridge for a physical network
PEER_INTEGRATION_PREFIX = 'int-'
PEER_PHYSICAL_PREFIX = 'phy-'

# Nonexistent peer used to create patch ports without associating them, it
# allows to define flows before association
NONEXISTENT_PEER = 'nonexistent-peer'

# The different types of tunnels
TUNNEL_NETWORK_TYPES = [p_const.TYPE_GRE, p_const.TYPE_VXLAN,
                        p_const.TYPE_GENEVE]

# --- OpenFlow table IDs

# --- Integration bridge (int_br)

LOCAL_SWITCHING = 0

# Various tables for DVR use of integration bridge flows
DVR_TO_SRC_MAC = 1
DVR_TO_SRC_MAC_VLAN = 2

CANARY_TABLE = 23

# Table for ARP poison/spoofing prevention rules
ARP_SPOOF_TABLE = 24

# Table for MAC spoof filtering
MAC_SPOOF_TABLE = 25

# DVR pre QOS table
DVR_PRE_QOS_TABLE = 56
# DVR post QOS table
DVR_POST_QOS_TABLE = 59
# packet rate limit table
PACKET_RATE_LIMIT = 57
BANDWIDTH_RATE_LIMIT = 58

# Table to decide whether further filtering is needed
TRANSIENT_TABLE = 60
LOCAL_MAC_DIRECT = 61
TRANSIENT_EGRESS_TABLE = 62
TRAFFIC_MIRROR = 65

# Table for DHCP
DHCP_IPV4_TABLE = 77
DHCP_IPV6_TABLE = 78
ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE = 83

# Tables used for ovs firewall
BASE_EGRESS_TABLE = 71
RULES_EGRESS_TABLE = 72
ACCEPT_OR_INGRESS_TABLE = 73
BASE_INGRESS_TABLE = 81
RULES_INGRESS_TABLE = 82

OVS_FIREWALL_TABLES = (
    BASE_EGRESS_TABLE,
    RULES_EGRESS_TABLE,
    ACCEPT_OR_INGRESS_TABLE,
    BASE_INGRESS_TABLE,
    RULES_INGRESS_TABLE,
    ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE,
)

# Tables for port dscp learn attrs with flows
DSCP_LEARN_TABLE = 70
DSCP_TABLE = 87

OVS_FW_INGRESS_TABLES = (
    ACCEPT_OR_INGRESS_TABLE,
    BASE_INGRESS_TABLE,
    RULES_INGRESS_TABLE,
)

OVS_FW_EGRESS_TABLES = (
    BASE_EGRESS_TABLE,
    RULES_EGRESS_TABLE,
)

# Tables for parties interacting with ovs firewall
ACCEPTED_EGRESS_TRAFFIC_TABLE = 91
ACCEPTED_INGRESS_TRAFFIC_TABLE = 92
DROPPED_TRAFFIC_TABLE = 93

ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE = 94

# Tables used for private floating network
PFN_ARP_RESPONSER_TABLE = 100
PFN_BASE_EGRESS_TABLE = 101
PFN_RULES_ROUTE_EGRESS_TABLE = 102
PFN_RULES_EGRESS_TABLE = 103
PFN_BASE_INGRESS_TABLE = 104
PFN_RULES_INGRESS_TABLE = 105
##
PFN_EGRESS_TRAFFIC_TABLE = 106
PFN_INGRESS_TRAFFIC_TABLE = 107

# Table used for fip metering
FIP_METERING_BASE_INGRESS_TABLE = 199
FIP_METERING_INGRESS_TABLE = 200
FIP_METERING_EGRESS_TABLE = 201

# Table used for traffic mirror
# 65 -> 220 -> 221 -> 222 -> 223 -> br-mirror
TRAFFIC_MIRROR_FILTER_TABLE = 220
TRAFFIC_MIRROR_METER_BANDWIDTH_TABLE = 221
TRAFFIC_MIRROR_METER_PPS_TABLE = 222
TRAFFIC_MIRROR_OUTPUT_TABLE = 223
##

# Table used for flow log
FLOW_LOG_REENTRY_TABLE = 149
FLOW_LOG_CLASSIFY_TABLE = 150
FLOW_LOG_ACCEPT_INGRESS_OUTPUT = 151
FLOW_LOG_ACCEPT_INGRESS_DROP = 152
FLOW_LOG_ACCEPT_EGRESS_OUTPUT = 153
FLOW_LOG_ACCEPT_EGRESS_DROP = 154


INT_BR_ALL_TABLES = (
    LOCAL_SWITCHING,
    DVR_TO_SRC_MAC,
    DVR_TO_SRC_MAC_VLAN,
    CANARY_TABLE,
    ARP_SPOOF_TABLE,
    MAC_SPOOF_TABLE,
    LOCAL_MAC_DIRECT,
    DVR_PRE_QOS_TABLE,
    DVR_POST_QOS_TABLE,
    PACKET_RATE_LIMIT,
    BANDWIDTH_RATE_LIMIT,
    TRANSIENT_TABLE,
    TRANSIENT_EGRESS_TABLE,
    DSCP_LEARN_TABLE,
    BASE_EGRESS_TABLE,
    RULES_EGRESS_TABLE,
    ACCEPT_OR_INGRESS_TABLE,
    DHCP_IPV4_TABLE,
    DHCP_IPV6_TABLE,
    BASE_INGRESS_TABLE,
    RULES_INGRESS_TABLE,
    ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE,
    DSCP_TABLE,
    ACCEPTED_EGRESS_TRAFFIC_TABLE,
    ACCEPTED_INGRESS_TRAFFIC_TABLE,
    DROPPED_TRAFFIC_TABLE,
    ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
    PFN_ARP_RESPONSER_TABLE,
    PFN_BASE_EGRESS_TABLE,
    PFN_RULES_ROUTE_EGRESS_TABLE,
    PFN_RULES_EGRESS_TABLE,
    PFN_BASE_INGRESS_TABLE,
    PFN_RULES_INGRESS_TABLE,
    PFN_EGRESS_TRAFFIC_TABLE,
    PFN_INGRESS_TRAFFIC_TABLE,
    FLOW_LOG_REENTRY_TABLE,
    FLOW_LOG_CLASSIFY_TABLE,
    TRAFFIC_MIRROR,
    TRAFFIC_MIRROR_FILTER_TABLE,
    TRAFFIC_MIRROR_METER_BANDWIDTH_TABLE,
    TRAFFIC_MIRROR_METER_PPS_TABLE,
    TRAFFIC_MIRROR_OUTPUT_TABLE,
)

# --- Tunnel bridge (tun_br)

# Various tables for tunneling flows
DVR_PROCESS = 1
PATCH_LV_TO_TUN = 2
GRE_TUN_TO_LV = 3
VXLAN_TUN_TO_LV = 4
GENEVE_TUN_TO_LV = 6

DVR_NOT_LEARN = 9
LEARN_FROM_TUN = 10
UCAST_TO_TUN = 20
ARP_RESPONDER = 21
FLOOD_TO_TUN = 22

TUN_BR_ALL_TABLES = (
    LOCAL_SWITCHING,
    DVR_PROCESS,
    PATCH_LV_TO_TUN,
    GRE_TUN_TO_LV,
    VXLAN_TUN_TO_LV,
    GENEVE_TUN_TO_LV,
    DVR_NOT_LEARN,
    LEARN_FROM_TUN,
    UCAST_TO_TUN,
    ARP_RESPONDER,
    FLOOD_TO_TUN)

# --- Physical Bridges (phys_brs)

# Various tables for DVR use of physical bridge flows
DVR_PROCESS_VLAN = 1
LOCAL_VLAN_TRANSLATION = 2
DVR_NOT_LEARN_VLAN = 3
PHYSICAL_INRESS_OUTPUT = 4
PHYSICAL_INRESS_NORMAL = 5
PHYSICAL_EGRESS_OUTPUT = 6
PHYSICAL_EGRESS_NORMAL = 7

NP_EGRESS_NAT = 80
NP_INGRESS_SRC_PORT = 81
NP_EGRESS_DEST_MAC_LEARN = 82
NP_EGRESS_NAT_CLASSIFY = 83
NP_EGRESS_ICMP = 87
NP_EGRESS_TCP = 88
NP_EGRESS_UDP = 89
NP_PROVIDER_IP_ARP_RESPONDER = 90
NP_INGRESS_DST_DIRECT = 91
NP_INGRESS_ALLOWED_SOURCES = 92
NP_INGRESS_ALLOWED_DIRECT = 93
NP_EGRESS_NORMAL = 100


PHY_BR_ALL_TABLES = (
    LOCAL_SWITCHING,
    DVR_PROCESS_VLAN,
    LOCAL_VLAN_TRANSLATION,
    DVR_NOT_LEARN_VLAN,
    PHYSICAL_INRESS_OUTPUT,
    PHYSICAL_INRESS_NORMAL,
    PHYSICAL_EGRESS_OUTPUT,
    PHYSICAL_EGRESS_NORMAL,
    NP_EGRESS_NAT,
    NP_INGRESS_SRC_PORT,
    NP_EGRESS_NAT_CLASSIFY,
    NP_EGRESS_ICMP,
    NP_EGRESS_TCP,
    NP_EGRESS_UDP,
    NP_PROVIDER_IP_ARP_RESPONDER,
    NP_INGRESS_DST_DIRECT,
    NP_INGRESS_ALLOWED_SOURCES,
    NP_INGRESS_ALLOWED_DIRECT,
)

# --- end of OpenFlow table IDs

# type for ARP reply in ARP header
ARP_REPLY = '0x2'

# Map tunnel types to tables number
TUN_TABLE = {p_const.TYPE_GRE: GRE_TUN_TO_LV,
             p_const.TYPE_VXLAN: VXLAN_TUN_TO_LV,
             p_const.TYPE_GENEVE: GENEVE_TUN_TO_LV}


# The default respawn interval for the ovsdb monitor
DEFAULT_OVSDBMON_RESPAWN = 30

# Represent invalid OF Port
OFPORT_INVALID = -1

ARP_RESPONDER_ACTIONS = ('move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],'
                         'mod_dl_src:%(mac)s,'
                         'load:0x2->NXM_OF_ARP_OP[],'
                         'move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],'
                         'move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],'
                         'load:%(mac)#x->NXM_NX_ARP_SHA[],'
                         'load:%(ip)#x->NXM_OF_ARP_SPA[],'
                         'in_port')

# Represent ovs status
OVS_RESTARTED = 0
OVS_NORMAL = 1
OVS_DEAD = 2

EXTENSION_DRIVER_TYPE = 'ovs'

# ovs datapath types
OVS_DATAPATH_SYSTEM = 'system'
OVS_DATAPATH_NETDEV = 'netdev'
OVS_DPDK_VHOST_USER = 'dpdkvhostuser'
OVS_DPDK_VHOST_USER_CLIENT = 'dpdkvhostuserclient'
OVS_DPDK_VDPA = 'dpdkvdpa'

OVS_DPDK_PORT_TYPES = [OVS_DPDK_VHOST_USER, OVS_DPDK_VHOST_USER_CLIENT,
                       OVS_DPDK_VDPA]

# default ovs vhost-user socket location
VHOST_USER_SOCKET_DIR = '/var/run/openvswitch'

MAX_DEVICE_RETRIES = 5

# OpenFlow version constants
OPENFLOW10 = "OpenFlow10"
OPENFLOW11 = "OpenFlow11"
OPENFLOW12 = "OpenFlow12"
OPENFLOW13 = "OpenFlow13"
OPENFLOW14 = "OpenFlow14"
OPENFLOW15 = "OpenFlow15"

OPENFLOW_MAX_PRIORITY = 65535

# define OFPP_UNSET      OFP_PORT_C(0xfff7) /* For OXM_OF_ACTSET_OUTPUT only.*/
# define OFPP_IN_PORT    OFP_PORT_C(0xfff8) /* Where the packet came in. */
# define OFPP_TABLE      OFP_PORT_C(0xfff9) /* Perform actions in flow table.*/
# define OFPP_NORMAL     OFP_PORT_C(0xfffa) /* Process with normal L2/L3.*/
# define OFPP_FLOOD      OFP_PORT_C(0xfffb) /* All ports except input port and
#                                            * ports disabled by STP. */
# define OFPP_ALL        OFP_PORT_C(0xfffc) /* All ports except input port.*/
# define OFPP_CONTROLLER OFP_PORT_C(0xfffd) /* Send to controller. */
# define OFPP_LOCAL      OFP_PORT_C(0xfffe) /* Local openflow "port". */
# define OFPP_NONE       OFP_PORT_C(0xffff) /* Not associated with any port.*/
OFPP_UNSET = int(0xfff7)
OFPP_IN_PORT = int(0xfff8)
OFPP_TABLE = int(0xfff9)
OFPP_NORMAL = int(0xfffa)
OFPP_FLOOD = int(0xfffb)
OFPP_ALL = int(0xfffc)
OFPP_CONTROLLER = int(0xfffd)
OFPP_LOCAL = int(0xfffe)
OFPP_NONE = int(0xffff)

# 12 bit OFPP_NONE
REG_DENY_MARK = int(0xfff)

# A placeholder for dead vlans.
DEAD_VLAN_TAG = p_const.MAX_VLAN_TAG + 1

# callback resource for setting 'bridge_name' in the 'binding:vif_details'
OVS_BRIDGE_NAME = 'ovs_bridge_name'

# callback resource for notifying to ovsdb handler
OVSDB_RESOURCE = 'ovsdb'

# Used in ovs port 'external_ids' in order mark it for no cleanup when
# ovs_cleanup script is used.
SKIP_CLEANUP = 'skip_cleanup'

REG_DVR_INTERFACE_MARK_LOCAL = 1
REG_DVR_INTERFACE_MARK_NONLOCAL = 2
