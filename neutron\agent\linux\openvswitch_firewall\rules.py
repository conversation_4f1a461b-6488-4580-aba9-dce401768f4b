# Copyright 2015 Red Hat, Inc.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import collections
import copy

import netaddr
from neutron_lib import constants as n_consts
from oslo_config import cfg

from neutron.agent import firewall
from neutron.agent.linux.openvswitch_firewall import constants as ovsfw_consts
from neutron.common import utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common import constants \
        as ovs_consts

CT_STATES = [
    ovsfw_consts.OF_STATE_ESTABLISHED_NOT_REPLY,
    ovsfw_consts.OF_STATE_NEW_NOT_ESTABLISHED]

FLOW_FIELD_FOR_IPVER_AND_DIRECTION = {
    (n_consts.IP_VERSION_4, n_consts.EGRESS_DIRECTION): 'nw_dst',
    (n_consts.IP_VERSION_6, n_consts.EGRESS_DIRECTION): 'ipv6_dst',
    (n_consts.IP_VERSION_4, n_consts.INGRESS_DIRECTION): 'nw_src',
    (n_consts.IP_VERSION_6, n_consts.INGRESS_DIRECTION): 'ipv6_src',
}

FORBIDDEN_PREFIXES = (n_consts.IPv4_ANY, n_consts.IPv6_ANY)
PRIORITY_GAP = 10
MAX_ALLOWED_PRI = 6000


def is_valid_prefix(ip_prefix):
    # IPv6 have multiple ways how to describe ::/0 network, converting to
    # IPNetwork and back to string unifies it
    return (ip_prefix and
            str(netaddr.IPNetwork(ip_prefix)) not in FORBIDDEN_PREFIXES)


def _assert_mergeable_rules(rule_conj_list):
    """Assert a given (rule, conj_ids) list has mergeable rules.

    The given rules must be the same except for port_range_{min,max}
    differences.
    """
    rule_tmpl = rule_conj_list[0][0].copy()
    rule_tmpl.pop('port_range_min', None)
    rule_tmpl.pop('port_range_max', None)
    for rule, conj_id in rule_conj_list[1:]:
        rule1 = rule.copy()
        rule1.pop('port_range_min', None)
        rule1.pop('port_range_max', None)
        if rule_tmpl != rule1:
            raise RuntimeError(
                "Incompatible SG rules detected: %(rule1)s and %(rule2)s. "
                "They cannot be merged. This should not happen." %
                {'rule1': rule_tmpl, 'rule2': rule})


def merge_common_rules(rule_conj_list):
    """Take a list of (rule, conj_id) and merge elements with the same rules.
    Return a list of (rule, conj_id_list).
    """
    if len(rule_conj_list) == 1:
        rule, conj_id = rule_conj_list[0]
        return [(rule, [conj_id])]

    _assert_mergeable_rules(rule_conj_list)
    rule_conj_map = collections.defaultdict(list)
    for rule, conj_id in rule_conj_list:
        rule_conj_map[(rule.get('port_range_min'),
                       rule.get('port_range_max'))].append(conj_id)

    result = []
    rule_tmpl = rule_conj_list[0][0]
    rule_tmpl.pop('port_range_min', None)
    rule_tmpl.pop('port_range_max', None)
    for (port_min, port_max), conj_ids in rule_conj_map.items():
        rule = rule_tmpl.copy()
        if port_min is not None:
            rule['port_range_min'] = port_min
        if port_max is not None:
            rule['port_range_max'] = port_max
        result.append((rule, conj_ids))
    return result


def _merge_port_ranges_helper(port_range_item):
    # Sort with 'port' but 'min' things must come first.
    port, m, dummy = port_range_item
    return port * 2 + (0 if m == 'min' else 1)


def merge_port_ranges(rule_conj_list):
    """Take a list of (rule, conj_id) and transform into a list
    whose rules don't overlap. Return a list of (rule, conj_id_list).
    """
    if len(rule_conj_list) == 1:
        rule, conj_id = rule_conj_list[0]
        return [(rule, [conj_id])]

    _assert_mergeable_rules(rule_conj_list)
    port_ranges = []
    for rule, conj_id in rule_conj_list:
        port_ranges.append((rule.get('port_range_min', 1), 'min', conj_id))
        port_ranges.append((rule.get('port_range_max', 65535), 'max', conj_id))

    port_ranges.sort(key=_merge_port_ranges_helper)

    # The idea here is to scan the port_ranges list in an ascending order,
    # keeping active conjunction IDs and range in cur_conj and cur_range_min.
    # A 'min' port_ranges item means an addition to cur_conj, while a 'max'
    # item means a removal.
    result = []
    rule_tmpl = rule_conj_list[0][0]
    cur_conj = {}
    cur_range_min = None
    for port, m, conj_id in port_ranges:
        if m == 'min':
            if conj_id in cur_conj:
                cur_conj[conj_id] += 1
                continue
            if cur_conj and cur_range_min != port:
                rule = rule_tmpl.copy()
                rule['port_range_min'] = cur_range_min
                rule['port_range_max'] = port - 1
                result.append((rule, list(cur_conj.keys())))
            cur_range_min = port
            cur_conj[conj_id] = 1
        else:
            if cur_conj[conj_id] > 1:
                cur_conj[conj_id] -= 1
                continue
            if cur_range_min <= port:
                rule = rule_tmpl.copy()
                rule['port_range_min'] = cur_range_min
                rule['port_range_max'] = port
                result.append((rule, list(cur_conj.keys())))
                # The next port range without 'port' starts from (port + 1)
                cur_range_min = port + 1
            del cur_conj[conj_id]

    if (len(result) == 1 and result[0][0]['port_range_min'] == 1 and
            result[0][0]['port_range_max'] == 65535):
        del result[0][0]['port_range_min']
        del result[0][0]['port_range_max']
    return result


def flow_priority_offset(rule, conjunction=False):
    """Calculate flow priority offset from rule.
    Whether the rule belongs to conjunction flows or not is decided
    upon existence of rule['remote_group_id'] but can be overridden
    to be True using the optional conjunction arg.
    """
    priority = deal_with_user_priority(rule.get('priority', 0), conjunction)
    conj_offset = 0 if 'remote_group_id' in rule or conjunction else 4
    protocol = rule.get('protocol')
    if protocol is None:
        return conj_offset + priority

    if protocol in [n_consts.PROTO_NUM_ICMP, n_consts.PROTO_NUM_IPV6_ICMP]:
        if 'port_range_min' not in rule:
            return conj_offset + 1 + priority
        elif 'port_range_max' not in rule:
            return conj_offset + 2 + priority
    return conj_offset + 3 + priority


def deal_with_user_priority(priority=0, is_conjunction=False):
    """Remote group rule does not support to set priority"""
    if is_conjunction or priority in [0, 1, None]:
        return 0
    elif not is_conjunction and priority > 1:
        return min(priority * PRIORITY_GAP, MAX_ALLOWED_PRI)


def create_flows_from_rule_and_port(rule, port, conjunction=False,
                                    enable_flow_log=False):
    """Create flows from given args.
    For description of the optional conjunction arg, see flow_priority_offset.
    """
    ethertype = rule['ethertype']
    direction = rule['direction']
    dst_ip_prefix = rule.get('dest_ip_prefix')
    src_ip_prefix = rule.get('source_ip_prefix')

    flow_template = {
        'priority': 70 + flow_priority_offset(rule, conjunction),
        'dl_type': ovsfw_consts.ethertype_to_dl_type_map[ethertype],
        'reg_port': port.ofport,
    }

    if is_valid_prefix(dst_ip_prefix):
        flow_template[FLOW_FIELD_FOR_IPVER_AND_DIRECTION[(
            utils.get_ip_version(dst_ip_prefix), n_consts.EGRESS_DIRECTION)]
        ] = dst_ip_prefix

    if is_valid_prefix(src_ip_prefix):
        flow_template[FLOW_FIELD_FOR_IPVER_AND_DIRECTION[(
            utils.get_ip_version(src_ip_prefix), n_consts.INGRESS_DIRECTION)]
        ] = src_ip_prefix

    flows = create_protocol_flows(direction, flow_template, port, rule,
                                  enable_flow_log)

    return flows


def get_flow_log_action(cookie, direction, eth_type, ip_proto,
                        action=ovsfw_consts.ACTION_ACCEPT,
                        learn_actions=None):
    flow_log_timeout = cfg.CONF.FLOW_LOG.flow_log_timeout
    proto_str = firewall.write_proto(eth_type, ip_proto)
    port_dst_str = ""
    port_src_str = ""
    if ip_proto in [n_consts.PROTO_NAME_TCP,
                    n_consts.PROTO_NAME_UDP]:
        port_dst_str = \
            "NXM_OF_%(ip_proto)s_DST[]=NXM_OF_%(ip_proto)s_DST[]," \
            % {'ip_proto': ip_proto.upper()}
        port_src_str = \
            "NXM_OF_%(ip_proto)s_SRC[]=NXM_OF_%(ip_proto)s_SRC[]," \
            % {'ip_proto': ip_proto.upper()}

    # The match is in same order, packet 5-tuple must mach the src to src
    # and dst to dst.
    mac_match = ("NXM_OF_ETH_SRC[]=NXM_OF_ETH_SRC[],"
                 "NXM_OF_ETH_DST[]=NXM_OF_ETH_DST[],")
    if eth_type == n_consts.IPv4:
        ip_dst = "NXM_OF_IP_DST[]=NXM_OF_IP_DST[],"
        ip_src = "NXM_OF_IP_SRC[]=NXM_OF_IP_SRC[],"
    else:
        ip_dst = "NXM_NX_IPV6_DST[]=NXM_NX_IPV6_DST[],"
        ip_src = "NXM_NX_IPV6_SRC[]=NXM_NX_IPV6_SRC[],"

    if direction == n_consts.INGRESS_DIRECTION:
        if action == firewall.RULE_ACTION_ACCEPT:
            fl_learn_table = ovs_consts.FLOW_LOG_ACCEPT_INGRESS_OUTPUT
        else:
            fl_learn_table = ovs_consts.FLOW_LOG_ACCEPT_INGRESS_DROP
    else:
        if action == firewall.RULE_ACTION_ACCEPT:
            fl_learn_table = ovs_consts.FLOW_LOG_ACCEPT_EGRESS_OUTPUT
        else:
            fl_learn_table = ovs_consts.FLOW_LOG_ACCEPT_EGRESS_DROP

    base_params = {
        'cookie': cookie,
        'table': fl_learn_table,
        'priority': 1,
        'idle_timeout': flow_log_timeout,
        'proto': proto_str,
        'mac_match': mac_match,
        'ip_src': ip_src,
        'ip_dst': ip_dst,
        'port_dst': port_dst_str,
        'port_src': port_src_str
    }
    learn_template = (
        "learn(cookie=%(cookie)s,"
        "table=%(table)s,"
        "priority=%(priority)s,"
        "idle_timeout=%(idle_timeout)s,"
        "%(proto)s,%(mac_match)s"
        "%(ip_src)s%(ip_dst)s%(port_dst)s%(port_src)s"
        "NXM_NX_REG10[0..11]{0})")
    learn_actions = learn_template.format(
        ",%(learn_actions)s" if learn_actions else "") % base_params
    if learn_actions:
        learn_actions %= {'learn_actions': learn_actions}

    if direction == n_consts.INGRESS_DIRECTION:
        if action == firewall.RULE_ACTION_ACCEPT:
            return "%(learn_actions)s," \
                   "resubmit(,%(table)s)" % \
                   {'learn_actions': learn_actions,
                    'table': ovs_consts.FLOW_LOG_ACCEPT_INGRESS_OUTPUT}
        else:
            return "%(learn_actions)s," \
                   "resubmit(,%(table)s)" % \
                   {'learn_actions': learn_actions,
                    'table': ovs_consts.FLOW_LOG_ACCEPT_INGRESS_DROP}
    else:
        if action == firewall.RULE_ACTION_ACCEPT:
            return "%(learn_actions)s," \
                   "resubmit(,%(table)s)" % \
                   {'learn_actions': learn_actions,
                    'table': ovs_consts.FLOW_LOG_ACCEPT_EGRESS_OUTPUT}
        else:
            return "%(learn_actions)s," \
                   "resubmit(,%(table)s)" % \
                   {'learn_actions': learn_actions,
                    'table': ovs_consts.FLOW_LOG_ACCEPT_EGRESS_DROP}


def _get_flow_log_actions(action, direction, enable_flow_log=False):
    """Get flow log actions according by action(ACCEPT OR DENY) and
    direction(INGRESS OR EGRESS)
    """
    if not enable_flow_log:
        return ''
    flow_log_actions = ''
    deny_action = ',{:s}'.format(ovsfw_consts.ACTION_SET_DENY)
    accept_action = ',{:s}'.format(ovsfw_consts.ACTION_SET_ACCEPT)
    if cfg.CONF.FLOW_LOG.enable_flow_log:
        if direction == n_consts.INGRESS_DIRECTION:
            flow_log_actions = ',{:s},resubmit(,{:d})'.format(
                ovsfw_consts.ACTION_SET_NET,
                ovs_consts.FLOW_LOG_REENTRY_TABLE)
        else:
            flow_log_actions = ',{:s},resubmit(,{:d})'.format(
                ovsfw_consts.ACTION_SET_NET,
                ovs_consts.FLOW_LOG_CLASSIFY_TABLE)
        if action == ovsfw_consts.ACTION_DENY:
            flow_log_actions = deny_action + flow_log_actions
        else:
            flow_log_actions = accept_action + flow_log_actions
    return flow_log_actions


def _get_lbaas_dscp_actions(port):
    """Get LBaaS dscp actions in the ingress direction"""
    actions = ''
    if cfg.CONF.AGENT.enable_lbaas_dscp and hasattr(port, 'neutron_port_dict'):
        port_info = port.neutron_port_dict
        port_attrs = port_info.get('cloud_attributes')
        if port_attrs and "dscp_learn" in port_attrs['cloud_attrs']:
            actions = ',resubmit(,{:d})'.format(
                ovs_consts.DSCP_TABLE)
    return actions


def populate_flow_common(direction, flow_template, port,
                         action=ovsfw_consts.ACTION_ACCEPT,
                         enable_flow_log=False):
    """Initialize common flow fields."""
    # Resubmit flow log processing pipeline to record packets usage.
    flow_log_actions = _get_flow_log_actions(action, direction,
                                             enable_flow_log)
    actions = ''
    if direction == n_consts.INGRESS_DIRECTION:
        flow_template['table'] = ovs_consts.RULES_INGRESS_TABLE
        actions = _get_lbaas_dscp_actions(port)
        if cfg.CONF.AGENT.across_sg_normal:
            flow_template['actions'] = (
                'resubmit(,{:d}){:s}{:s}'.format(
                    ovs_consts.ACCEPTED_INGRESS_TRAFFIC_NORMAL_TABLE,
                    actions,
                    flow_log_actions)
                if action != ovsfw_consts.ACTION_DENY else
                'resubmit(,{:d}){:s}'.format(
                    ovs_consts.DROPPED_TRAFFIC_TABLE, flow_log_actions))
        else:
            flow_template['actions'] = (
                "output:{:d}{:s}{:s}".format(port.ofport,
                                             actions,
                                             flow_log_actions)
                if action != ovsfw_consts.ACTION_DENY else
                'resubmit(,{:d}){:s}'.format(
                    ovs_consts.DROPPED_TRAFFIC_TABLE, flow_log_actions))
    elif direction == n_consts.EGRESS_DIRECTION:
        flow_template['table'] = ovs_consts.RULES_EGRESS_TABLE
        # Traffic can be both ingress and egress, check that no ingress rules
        # should be applied
        if action == ovsfw_consts.ACTION_DENY:
            actions = "resubmit(,{:d}){:s}".format(
                ovs_consts.DROPPED_TRAFFIC_TABLE, flow_log_actions)
        else:
            actions = 'resubmit(,{:d}){:s}'.format(
                ovs_consts.ACCEPT_OR_INGRESS_TABLE, flow_log_actions)
        flow_template['actions'] = actions

    return flow_template


def create_protocol_flows(direction, flow_template, port, rule,
                          enable_flow_log=False):
    flow_template = populate_flow_common(
        direction,
        flow_template.copy(),
        port, rule.get('action', ovsfw_consts.ACTION_ACCEPT), enable_flow_log)
    flows = []
    protocol = rule.get('protocol')
    if protocol is not None:
        flow_template['nw_proto'] = protocol
    if protocol in [n_consts.PROTO_NUM_ICMP, n_consts.PROTO_NUM_IPV6_ICMP]:
        flows += create_icmp_flows(flow_template, rule)
    else:
        flows += create_port_range_flows(flow_template, rule)
    return flows or [flow_template]


def create_port_range_flows(flow_template, rule):
    protocol = ovsfw_consts.REVERSE_IP_PROTOCOL_MAP_WITH_PORTS.get(
        rule.get('protocol'))
    if protocol is None:
        return []
    flows = []
    src_port_match = '{:s}_src'.format(protocol)
    src_port_min = rule.get('source_port_range_min')
    src_port_max = rule.get('source_port_range_max')
    dst_port_match = '{:s}_dst'.format(protocol)
    dst_port_min = rule.get('port_range_min')
    dst_port_max = rule.get('port_range_max')

    dst_port_range = []
    if dst_port_min and dst_port_max:
        dst_port_range = utils.port_rule_masking(dst_port_min, dst_port_max)

    src_port_range = []
    if src_port_min and src_port_max:
        src_port_range = utils.port_rule_masking(src_port_min, src_port_max)
        for port in src_port_range:
            flow = flow_template.copy()
            flow[src_port_match] = port
            if dst_port_range:
                for port in dst_port_range:
                    dst_flow = flow.copy()
                    dst_flow[dst_port_match] = port
                    flows.append(dst_flow)
            else:
                flows.append(flow)
    else:
        for port in dst_port_range:
            flow = flow_template.copy()
            flow[dst_port_match] = port
            flows.append(flow)

    return flows


def create_icmp_flows(flow_template, rule):
    icmp_type = rule.get('port_range_min')
    if icmp_type is None:
        return []
    flow = flow_template.copy()
    flow['icmp_type'] = icmp_type

    icmp_code = rule.get('port_range_max')
    if icmp_code is not None:
        flow['icmp_code'] = icmp_code
    return [flow]


def _flow_priority_offset_from_conj_id(conj_id):
    "Return a flow priority offset encoded in a conj_id."
    # A base conj_id, which is returned by ConjIdMap.get_conj_id, is a
    # multiple of 8, and we use 2 conj_ids per offset.
    return conj_id % 8 // 2


def create_flows_for_ip_address(ip_address, direction, ethertype,
                                vlan_tag, conj_ids):
    """Create flows from a rule and an ip_address derived from
    remote_group_id
    """
    ip_address, mac_address = ip_address
    net = netaddr.IPNetwork(str(ip_address))
    any_src_ip = net.prefixlen == 0

    # Group conj_ids per priority.
    conj_id_lists = [[] for i in range(4)]
    for conj_id in conj_ids:
        conj_id_lists[
            _flow_priority_offset_from_conj_id(conj_id)].append(conj_id)

    ip_prefix = str(netaddr.IPNetwork(ip_address).cidr)

    flow_template = {
        'dl_type': ovsfw_consts.ethertype_to_dl_type_map[ethertype],
        'reg_net': vlan_tag,  # needed for project separation
    }

    ip_ver = utils.get_ip_version(ip_prefix)

    if direction == n_consts.EGRESS_DIRECTION:
        flow_template['table'] = ovs_consts.RULES_EGRESS_TABLE
    elif direction == n_consts.INGRESS_DIRECTION:
        flow_template['table'] = ovs_consts.RULES_INGRESS_TABLE

    flow_template[FLOW_FIELD_FOR_IPVER_AND_DIRECTION[(
        ip_ver, direction)]] = ip_prefix

    if any_src_ip:
        flow_template['dl_src'] = mac_address

    result = []
    for offset, conj_id_list in enumerate(conj_id_lists):
        if not conj_id_list:
            continue
        flow_template['priority'] = 70 + offset
        result.extend(
            substitute_conjunction_actions([flow_template], 1, conj_id_list))
    return result


def _get_eth_type_and_proto(flow):
    dl_type = flow.get('dl_type')
    if dl_type == 0x86dd:
        eth_type = n_consts.IPv6
        nw_proto = flow.get('nw_proto')
        if nw_proto == 58:
            proto = n_consts.PROTO_NAME_ICMP
        elif nw_proto == 17:
            proto = n_consts.PROTO_NAME_UDP
        elif nw_proto == 6:
            proto = n_consts.PROTO_NAME_TCP
        else:
            proto = ''
    else:
        eth_type = n_consts.IPv4
        nw_proto = flow.get('nw_proto')
        if nw_proto == 1:
            proto = n_consts.PROTO_NAME_ICMP
        elif nw_proto == 17:
            proto = n_consts.PROTO_NAME_UDP
        elif nw_proto == 6:
            proto = n_consts.PROTO_NAME_TCP
        else:
            proto = ''

    return eth_type, proto


def create_accept_flows(flow, action=ovsfw_consts.ACTION_ACCEPT,
                        enable_flow_log=False):
    flow['ct_state'] = CT_STATES[0]
    f0 = copy.deepcopy(flow)
    f0_action = f0.get("actions", "")
    flow0 = copy.deepcopy(f0)
    flow0.pop('ct_state', None)
    if action == ovsfw_consts.ACTION_ACCEPT:
        flow0['reg11'] = 0
    else:
        flow0['reg11'] = 1
    if flow0.get('conj_id'):
        flow0['reg7'] = flow0['conj_id']
        flow0.pop('conj_id', None)
    eth_type, proto = _get_eth_type_and_proto(f0)
    # Change those flows have:
    # match: reg_port=N actions: output=N
    # to learn(to table=151) and resubmit(,151) to hit the flow_log and
    # output to the destination by flow_log output:reg9 eventually.
    if (enable_flow_log and
            (("output" in f0_action and "learn" not in f0_action) or
             flow['table'] == ovs_consts.RULES_INGRESS_TABLE)):
        fl_actions = get_flow_log_action(
            0, n_consts.INGRESS_DIRECTION, eth_type, proto, action)
        flow0["table"] = ovs_consts.FLOW_LOG_REENTRY_TABLE
        flow0["actions"] = fl_actions
    elif (enable_flow_log and
            flow['table'] == ovs_consts.RULES_EGRESS_TABLE):
        fl_actions = get_flow_log_action(
            0, n_consts.EGRESS_DIRECTION, eth_type, proto, action)
        flow0["table"] = ovs_consts.FLOW_LOG_CLASSIFY_TABLE
        flow0["actions"] = fl_actions
        if flow0.get('reg5'):
            flow0['in_port'] = flow0['reg5']
            flow0.pop('reg5', None)
        if flow0.get('reg_port'):
            flow0['in_port'] = flow0['reg_port']
            flow0.pop('reg_port', None)
    result = [f0]
    result_flow_log = [flow0]
    flow['ct_state'] = CT_STATES[1]
    f1 = copy.deepcopy(flow)
    flow_action = "resubmit(,{:d})".format(ovs_consts.FLOW_LOG_REENTRY_TABLE)
    ingress_action = "resubmit(,{:d})".format(
        ovs_consts.ACCEPTED_INGRESS_TRAFFIC_TABLE)
    if flow['table'] == ovs_consts.RULES_INGRESS_TABLE:
        if action == ovsfw_consts.ACTION_ACCEPT:
            fl_actions = f1['actions']
            if ingress_action not in fl_actions:
                fl_actions = "{:s},{:s}".format(fl_actions, ingress_action)
            if enable_flow_log and flow_action not in fl_actions:
                f1['actions'] = (
                    'ct(commit,zone=NXM_NX_REG{:d}[0..15]),'
                    '{:s},resubmit(,{:d})'.format(
                        ovsfw_consts.REG_NET,
                        fl_actions,
                        ovs_consts.FLOW_LOG_REENTRY_TABLE))
            else:
                f1['actions'] = (
                    'ct(commit,zone=NXM_NX_REG{:d}[0..15]),'
                    '{:s}'.format(
                        ovsfw_consts.REG_NET,
                        fl_actions))
        else:
            f1['actions'] = 'resubmit(,{:d})'.format(
                ovs_consts.DROPPED_TRAFFIC_TABLE)
            if enable_flow_log:
                f1['actions'] = '{:s},{:s},{:s},resubmit(,{:d})'.format(
                    f1['actions'],
                    ovsfw_consts.ACTION_SET_DENY,
                    ovsfw_consts.ACTION_SET_NET,
                    ovs_consts.FLOW_LOG_REENTRY_TABLE)
    result.append(f1)
    if enable_flow_log:
        return result + result_flow_log
    else:
        return result


def substitute_conjunction_actions(flows, dimension, conj_ids):
    result = []
    for flow in flows:
        for i in range(2):
            new_flow = flow.copy()
            new_flow['ct_state'] = CT_STATES[i]
            new_flow['actions'] = ','.join(
                ["conjunction(%d,%d/2)" % (s + i, dimension)
                 for s in conj_ids])
            result.append(new_flow)

    return result


def create_conj_flows(port, conj_id, direction, ethertype,
                      action=ovsfw_consts.ACTION_ACCEPT,
                      enable_flow_log=False):
    """Generate "accept" flows for a given conjunction ID."""
    flow_template = {
        'priority': 70 + _flow_priority_offset_from_conj_id(conj_id),
        'conj_id': conj_id,
        'dl_type': ovsfw_consts.ethertype_to_dl_type_map[ethertype],
        # This reg_port matching is for delete_all_port_flows.
        # The matching is redundant as it has been done by
        # conjunction(...,2/2) flows and flows can be summarized
        # without this.
        'reg_port': port.ofport,
    }
    flow_template = populate_flow_common(direction, flow_template, port,
                                         action, enable_flow_log)
    flows = create_accept_flows(flow_template, action, enable_flow_log)
    if enable_flow_log and len(flows) == 3:
        flows_conj = copy.deepcopy(flows[-1])
        flows_conj['reg7'] += 1
        flows.append(flows_conj)
    flows[1]['conj_id'] += 1
    return flows
